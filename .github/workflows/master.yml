name: Deploy

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Install Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '20' # <PERSON><PERSON><PERSON> phiên bản <PERSON>.js phù hợp

      - name: Install npm dependencies and build
        run: |
          npm install &&
          npm run build
      - name: SSH and deploy to EC2
        env:
          PRIVATE_KEY: ${{ secrets.VIIS_COMBINE_SECRET }}
          EC2_HOSTNAME: ${{ secrets.VIIS_COMBINE_IP_ADDRESS }}
          EC2_USER: ${{ secrets.USER_NAME }}
          REMOTE_PATH: /home/<USER>/iot-backend-typescript/dist
          BACKEND_PATH: /home/<USER>/iot-backend-typescript
        run: |
          echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
          rsync -avzh -e "ssh -o StrictHostKeyChecking=no -i private_key"  ./dist/ $EC2_USER@$EC2_HOSTNAME:$REMOTE_PATH
          rsync -avzh -e "ssh -o StrictHostKeyChecking=no -i private_key" ./package.json $EC2_USER@$EC2_HOSTNAME:$BACKEND_PATH
          ssh -o StrictHostKeyChecking=no -i private_key "${EC2_USER}@${EC2_HOSTNAME}" '
            # Now we have got the access of EC2 and we will start the deploy.
            cd /home/<USER>/iot-backend-typescript/ &&
            npm i &&
            pm2 restart ecosystem.config.js
          '
