<!DOCTYPE html><html class="default" lang="en"><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>ThingsboardAPIService | VIIS Backend Documentation - v1.0.0</title><meta name="description" content="Documentation for VIIS Backend Documentation"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">VIIS Backend Documentation - v1.0.0</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../index.html">VIIS Backend Documentation</a></li><li><a href="../modules/thingsboard_ThingsboardAPIService.html">thingsboard/ThingsboardAPIService</a></li><li><a href="thingsboard_ThingsboardAPIService.ThingsboardAPIService.html">ThingsboardAPIService</a></li></ul><h1>Class ThingsboardAPIService</h1></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/thingsboard/ThingsboardAPIService.ts#L21">modules/thingsboard/ThingsboardAPIService.ts:21</a></li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Constructors</h3><div class="tsd-index-list"><a href="thingsboard_ThingsboardAPIService.ThingsboardAPIService.html#constructor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Methods</h3><div class="tsd-index-list"><a href="thingsboard_ThingsboardAPIService.ThingsboardAPIService.html#controlDeviceOneway" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>control<wbr/>Device<wbr/>Oneway</span></a>
<a href="thingsboard_ThingsboardAPIService.ThingsboardAPIService.html#createDeviceProfile" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>create<wbr/>Device<wbr/>Profile</span></a>
<a href="thingsboard_ThingsboardAPIService.ThingsboardAPIService.html#getDeviceCredentials" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Device<wbr/>Credentials</span></a>
<a href="thingsboard_ThingsboardAPIService.ThingsboardAPIService.html#getDeviceProfile" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Device<wbr/>Profile</span></a>
<a href="thingsboard_ThingsboardAPIService.ThingsboardAPIService.html#getDeviceTimeseriesDataHistory" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Device<wbr/>Timeseries<wbr/>Data<wbr/>History</span></a>
<a href="thingsboard_ThingsboardAPIService.ThingsboardAPIService.html#getDeviceTimeseriesDataLatest" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Device<wbr/>Timeseries<wbr/>Data<wbr/>Latest</span></a>
<a href="thingsboard_ThingsboardAPIService.ThingsboardAPIService.html#getDeviceTimeseriesKeys" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Device<wbr/>Timeseries<wbr/>Keys</span></a>
<a href="thingsboard_ThingsboardAPIService.ThingsboardAPIService.html#getUserProfile" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>User<wbr/>Profile</span></a>
<a href="thingsboard_ThingsboardAPIService.ThingsboardAPIService.html#thingsboardLogin" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>thingsboard<wbr/>Login</span></a>
<a href="thingsboard_ThingsboardAPIService.ThingsboardAPIService.html#thingsboardRefreshToken" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>thingsboard<wbr/>Refresh<wbr/>Token</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Constructors"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Constructors</h2></summary><section><section class="tsd-panel tsd-member"><a id="constructor" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>constructor</span><a href="#constructor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="constructor.new_ThingsboardAPIService" class="tsd-anchor"></a><span class="tsd-kind-constructor-signature">new <wbr/>ThingsboardAPIService</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">thingsboardService</span>, <span class="tsd-kind-parameter">frappeService</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="thingsboard_ThingsboardAPIService.ThingsboardAPIService.html" class="tsd-signature-type tsd-kind-class">ThingsboardAPIService</a><a href="#constructor.new_ThingsboardAPIService" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">thingsboardService</span>: <span class="tsd-signature-type">ThingsboardService</span></span></li><li><span><span class="tsd-kind-parameter">frappeService</span>: <span class="tsd-signature-type">FrappeService</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="thingsboard_ThingsboardAPIService.ThingsboardAPIService.html" class="tsd-signature-type tsd-kind-class">ThingsboardAPIService</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/thingsboard/ThingsboardAPIService.ts#L22">modules/thingsboard/ThingsboardAPIService.ts:22</a></li></ul></aside></li></ul></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Methods"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Methods</h2></summary><section><section class="tsd-panel tsd-member"><a id="controlDeviceOneway" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>control<wbr/>Device<wbr/>Oneway</span><a href="#controlDeviceOneway" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="controlDeviceOneway.controlDeviceOneway-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">control<wbr/>Device<wbr/>Oneway</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">tb_device_id</span>, <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#controlDeviceOneway.controlDeviceOneway-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser</span></span></li><li><span><span class="tsd-kind-parameter">tb_device_id</span>: <span class="tsd-signature-type">string</span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">any</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/thingsboard/ThingsboardAPIService.ts#L35">modules/thingsboard/ThingsboardAPIService.ts:35</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="createDeviceProfile" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>create<wbr/>Device<wbr/>Profile</span><a href="#createDeviceProfile" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="createDeviceProfile.createDeviceProfile-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">create<wbr/>Device<wbr/>Profile</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#createDeviceProfile.createDeviceProfile-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">any</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/thingsboard/ThingsboardAPIService.ts#L27">modules/thingsboard/ThingsboardAPIService.ts:27</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getDeviceCredentials" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Device<wbr/>Credentials</span><a href="#getDeviceCredentials" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getDeviceCredentials.getDeviceCredentials-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Device<wbr/>Credentials</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span><a href="#getDeviceCredentials.getDeviceCredentials-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">any</span></span></li><li><span><span class="tsd-kind-parameter">params</span>: <span class="tsd-signature-type">any</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/thingsboard/ThingsboardAPIService.ts#L139">modules/thingsboard/ThingsboardAPIService.ts:139</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getDeviceProfile" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Device<wbr/>Profile</span><a href="#getDeviceProfile" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getDeviceProfile.getDeviceProfile-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Device<wbr/>Profile</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">deviceProfileId</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#getDeviceProfile.getDeviceProfile-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">deviceProfileId</span>: <span class="tsd-signature-type">string</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/thingsboard/ThingsboardAPIService.ts#L31">modules/thingsboard/ThingsboardAPIService.ts:31</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getDeviceTimeseriesDataHistory" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Device<wbr/>Timeseries<wbr/>Data<wbr/>History</span><a href="#getDeviceTimeseriesDataHistory" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getDeviceTimeseriesDataHistory.getDeviceTimeseriesDataHistory-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Device<wbr/>Timeseries<wbr/>Data<wbr/>History</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">tb_device_id</span>, <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">JsonObject</span><span class="tsd-signature-symbol">&gt;</span><a href="#getDeviceTimeseriesDataHistory.getDeviceTimeseriesDataHistory-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser</span></span></li><li><span><span class="tsd-kind-parameter">tb_device_id</span>: <span class="tsd-signature-type">string</span></span></li><li><span><span class="tsd-kind-parameter">params</span>: <span class="tsd-signature-type">TimeseriesData</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">JsonObject</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/thingsboard/ThingsboardAPIService.ts#L115">modules/thingsboard/ThingsboardAPIService.ts:115</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getDeviceTimeseriesDataLatest" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Device<wbr/>Timeseries<wbr/>Data<wbr/>Latest</span><a href="#getDeviceTimeseriesDataLatest" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getDeviceTimeseriesDataLatest.getDeviceTimeseriesDataLatest-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Device<wbr/>Timeseries<wbr/>Data<wbr/>Latest</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">tb_device_id</span>, <span class="tsd-kind-parameter">keys</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#getDeviceTimeseriesDataLatest.getDeviceTimeseriesDataLatest-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser</span></span></li><li><span><span class="tsd-kind-parameter">tb_device_id</span>: <span class="tsd-signature-type">string</span></span></li><li><span><span class="tsd-kind-parameter">keys</span>: <span class="tsd-signature-type">string</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/thingsboard/ThingsboardAPIService.ts#L93">modules/thingsboard/ThingsboardAPIService.ts:93</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getDeviceTimeseriesKeys" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Device<wbr/>Timeseries<wbr/>Keys</span><a href="#getDeviceTimeseriesKeys" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getDeviceTimeseriesKeys.getDeviceTimeseriesKeys-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Device<wbr/>Timeseries<wbr/>Keys</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">tb_device_id</span>, <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#getDeviceTimeseriesKeys.getDeviceTimeseriesKeys-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser</span></span></li><li><span><span class="tsd-kind-parameter">tb_device_id</span>: <span class="tsd-signature-type">string</span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">any</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/thingsboard/ThingsboardAPIService.ts#L70">modules/thingsboard/ThingsboardAPIService.ts:70</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getUserProfile" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>User<wbr/>Profile</span><a href="#getUserProfile" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getUserProfile.getUserProfile-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>User<wbr/>Profile</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span><a href="#getUserProfile.getUserProfile-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/thingsboard/ThingsboardAPIService.ts#L66">modules/thingsboard/ThingsboardAPIService.ts:66</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="thingsboardLogin" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>thingsboard<wbr/>Login</span><a href="#thingsboardLogin" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="thingsboardLogin.thingsboardLogin-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">thingsboard<wbr/>Login</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#thingsboardLogin.thingsboardLogin-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">any</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/thingsboard/ThingsboardAPIService.ts#L175">modules/thingsboard/ThingsboardAPIService.ts:175</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="thingsboardRefreshToken" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>thingsboard<wbr/>Refresh<wbr/>Token</span><a href="#thingsboardRefreshToken" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="thingsboardRefreshToken.thingsboardRefreshToken-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">thingsboard<wbr/>Refresh<wbr/>Token</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span><a href="#thingsboardRefreshToken.thingsboardRefreshToken-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/thingsboard/ThingsboardAPIService.ts#L171">modules/thingsboard/ThingsboardAPIService.ts:171</a></li></ul></aside></li></ul></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><span class="settings-label">Member Visibility</span><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li></ul></div><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="tsd-otp-Constructors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Constructors</summary><div><a href="#constructor" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="tsd-otp-Methods"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Methods</summary><div><a href="#controlDeviceOneway" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>control<wbr/>Device<wbr/>Oneway</span></a><a href="#createDeviceProfile" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>create<wbr/>Device<wbr/>Profile</span></a><a href="#getDeviceCredentials" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Device<wbr/>Credentials</span></a><a href="#getDeviceProfile" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Device<wbr/>Profile</span></a><a href="#getDeviceTimeseriesDataHistory" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Device<wbr/>Timeseries<wbr/>Data<wbr/>History</span></a><a href="#getDeviceTimeseriesDataLatest" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Device<wbr/>Timeseries<wbr/>Data<wbr/>Latest</span></a><a href="#getDeviceTimeseriesKeys" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Device<wbr/>Timeseries<wbr/>Keys</span></a><a href="#getUserProfile" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>User<wbr/>Profile</span></a><a href="#thingsboardLogin" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>thingsboard<wbr/>Login</span></a><a href="#thingsboardRefreshToken" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>thingsboard<wbr/>Refresh<wbr/>Token</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../index.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1"></use></svg><span>VIIS Backend Documentation - v1.0.0</span></a><ul class="tsd-small-nested-navigation" id="tsd-nav-container" data-base=".."><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
