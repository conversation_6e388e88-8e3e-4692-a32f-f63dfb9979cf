<!DOCTYPE html><html class="default" lang="en"><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>VisitorController | VIIS Backend Documentation - v1.0.0</title><meta name="description" content="Documentation for VIIS Backend Documentation"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">VIIS Backend Documentation - v1.0.0</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../index.html">VIIS Backend Documentation</a></li><li><a href="../modules/visitor_manage_VisitorController.html">visitor-manage/VisitorController</a></li><li><a href="visitor_manage_VisitorController.VisitorController.html">VisitorController</a></li></ul><h1>Class VisitorController</h1></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L26">modules/visitor-manage/VisitorController.ts:26</a></li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Constructors</h3><div class="tsd-index-list"><a href="visitor_manage_VisitorController.VisitorController.html#constructor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Methods</h3><div class="tsd-index-list"><a href="visitor_manage_VisitorController.VisitorController.html#createVisitor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>create<wbr/>Visitor</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#createVisitorCard" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>create<wbr/>Visitor<wbr/>Card</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#createVisitorLocation" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>create<wbr/>Visitor<wbr/>Location</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#createVisitorSession" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>create<wbr/>Visitor<wbr/>Session</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#deleteVisitor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete<wbr/>Visitor</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#deleteVisitorCard" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete<wbr/>Visitor<wbr/>Card</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#deleteVisitorLocation" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete<wbr/>Visitor<wbr/>Location</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#deleteVisitorSession" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete<wbr/>Visitor<wbr/>Session</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#getVisitorCardList" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Visitor<wbr/>Card<wbr/>List</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#getVisitorFreeCardList" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Visitor<wbr/>Free<wbr/>Card<wbr/>List</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#getVisitorList" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Visitor<wbr/>List</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#getVisitorLocationList" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Visitor<wbr/>Location<wbr/>List</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#getVisitorSessionHistoryList" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Visitor<wbr/>Session<wbr/>History<wbr/>List</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#getVisitorSessionList" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Visitor<wbr/>Session<wbr/>List</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#getVisitorSessionListStatistic" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Visitor<wbr/>Session<wbr/>List<wbr/>Statistic</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#updateVisitor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#updateVisitorCard" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Card</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#updateVisitorCreatedAll" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Created<wbr/>All</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#updateVisitorDeleted" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Deleted</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#updateVisitorLocation" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Location</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#updateVisitorSession" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Session</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#updateVisitorSync" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Sync</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#updateVisitorUpdated" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Updated</span></a>
<a href="visitor_manage_VisitorController.VisitorController.html#updateVisitorUpdatedAll" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Updated<wbr/>All</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Constructors"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Constructors</h2></summary><section><section class="tsd-panel tsd-member"><a id="constructor" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>constructor</span><a href="#constructor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="constructor.new_VisitorController" class="tsd-anchor"></a><span class="tsd-kind-constructor-signature">new <wbr/>Visitor<wbr/>Controller</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">visitorService</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="visitor_manage_VisitorController.VisitorController.html" class="tsd-signature-type tsd-kind-class">VisitorController</a><a href="#constructor.new_VisitorController" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">visitorService</span>: <a href="visitor_manage_VisitorService.VisitorService.html" class="tsd-signature-type tsd-kind-class">VisitorService</a></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="visitor_manage_VisitorController.VisitorController.html" class="tsd-signature-type tsd-kind-class">VisitorController</a></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L27">modules/visitor-manage/VisitorController.ts:27</a></li></ul></aside></li></ul></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Methods"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Methods</h2></summary><section><section class="tsd-panel tsd-member"><a id="createVisitor" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>create<wbr/>Visitor</span><a href="#createVisitor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="createVisitor.createVisitor-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">create<wbr/>Visitor</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#createVisitor.createVisitor-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">IIotVisitorInfor</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L98">modules/visitor-manage/VisitorController.ts:98</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="createVisitorCard" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>create<wbr/>Visitor<wbr/>Card</span><a href="#createVisitorCard" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="createVisitorCard.createVisitorCard-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">create<wbr/>Visitor<wbr/>Card</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#createVisitorCard.createVisitorCard-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">IIotVisitorCard</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L71">modules/visitor-manage/VisitorController.ts:71</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="createVisitorLocation" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>create<wbr/>Visitor<wbr/>Location</span><a href="#createVisitorLocation" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="createVisitorLocation.createVisitorLocation-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">create<wbr/>Visitor<wbr/>Location</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#createVisitorLocation.createVisitorLocation-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">IIotVisitorLocation</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L40">modules/visitor-manage/VisitorController.ts:40</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="createVisitorSession" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>create<wbr/>Visitor<wbr/>Session</span><a href="#createVisitorSession" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="createVisitorSession.createVisitorSession-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">create<wbr/>Visitor<wbr/>Session</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#createVisitorSession.createVisitorSession-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">IIotVisitorSession</span><span class="tsd-signature-symbol">[]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L168">modules/visitor-manage/VisitorController.ts:168</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="deleteVisitor" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>delete<wbr/>Visitor</span><a href="#deleteVisitor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="deleteVisitor.deleteVisitor-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">delete<wbr/>Visitor</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span><a href="#deleteVisitor.deleteVisitor-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">IIotVisitorInfor</span><span class="tsd-signature-symbol">[]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L140">modules/visitor-manage/VisitorController.ts:140</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="deleteVisitorCard" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>delete<wbr/>Visitor<wbr/>Card</span><a href="#deleteVisitorCard" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="deleteVisitorCard.deleteVisitorCard-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">delete<wbr/>Visitor<wbr/>Card</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span><a href="#deleteVisitorCard.deleteVisitorCard-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">params</span>: <span class="tsd-signature-symbol">{ </span><br/><span>    </span><span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span><br/><span class="tsd-signature-symbol">}</span></span><ul class="tsd-parameters"><li class="tsd-parameter"><h5><span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li></ul></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L83">modules/visitor-manage/VisitorController.ts:83</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="deleteVisitorLocation" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>delete<wbr/>Visitor<wbr/>Location</span><a href="#deleteVisitorLocation" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="deleteVisitorLocation.deleteVisitorLocation-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">delete<wbr/>Visitor<wbr/>Location</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span><a href="#deleteVisitorLocation.deleteVisitorLocation-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">params</span>: <span class="tsd-signature-symbol">{ </span><br/><span>    </span><span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span><br/><span class="tsd-signature-symbol">}</span></span><ul class="tsd-parameters"><li class="tsd-parameter"><h5><span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li></ul></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L52">modules/visitor-manage/VisitorController.ts:52</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="deleteVisitorSession" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>delete<wbr/>Visitor<wbr/>Session</span><a href="#deleteVisitorSession" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="deleteVisitorSession.deleteVisitorSession-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">delete<wbr/>Visitor<wbr/>Session</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span><a href="#deleteVisitorSession.deleteVisitorSession-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">params</span>: <span class="tsd-signature-symbol">{ </span><br/><span>    </span><span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span><br/><span class="tsd-signature-symbol">}</span></span><ul class="tsd-parameters"><li class="tsd-parameter"><h5><span class="tsd-kind-property">name</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li></ul></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L180">modules/visitor-manage/VisitorController.ts:180</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getVisitorCardList" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Visitor<wbr/>Card<wbr/>List</span><a href="#getVisitorCardList" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getVisitorCardList.getVisitorCardList-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Visitor<wbr/>Card<wbr/>List</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span><a href="#getVisitorCardList.getVisitorCardList-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">params</span>: <span class="tsd-signature-type">IGeneralDoc</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L59">modules/visitor-manage/VisitorController.ts:59</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getVisitorFreeCardList" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Visitor<wbr/>Free<wbr/>Card<wbr/>List</span><a href="#getVisitorFreeCardList" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getVisitorFreeCardList.getVisitorFreeCardList-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Visitor<wbr/>Free<wbr/>Card<wbr/>List</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span><a href="#getVisitorFreeCardList.getVisitorFreeCardList-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">params</span>: <span class="tsd-signature-type">IGeneralDoc</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L65">modules/visitor-manage/VisitorController.ts:65</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getVisitorList" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Visitor<wbr/>List</span><a href="#getVisitorList" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getVisitorList.getVisitorList-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Visitor<wbr/>List</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span><br/><span>    </span><span class="tsd-kind-property">data</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span><br/><span>    </span><span class="tsd-kind-property">pagination</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span><br/><span>        </span><span class="tsd-kind-property">pageNumber</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span><br/><span>        </span><span class="tsd-kind-property">pageSize</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span><br/><span>        </span><span class="tsd-kind-property">totalElements</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span><br/><span>        </span><span class="tsd-kind-property">totalPages</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span><br/><span>    </span><span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">; </span><br/><span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">&gt;</span><a href="#getVisitorList.getVisitorList-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><hr>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">params</span>: <span class="tsd-signature-type">IGeneralDoc</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span><br/><span>    </span><span class="tsd-kind-property">data</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span><br/><span>    </span><span class="tsd-kind-property">pagination</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span><br/><span>        </span><span class="tsd-kind-property">pageNumber</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span><br/><span>        </span><span class="tsd-kind-property">pageSize</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span><br/><span>        </span><span class="tsd-kind-property">totalElements</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span><br/><span>        </span><span class="tsd-kind-property">totalPages</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span><br/><span>    </span><span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">; </span><br/><span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">&gt;</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L92">modules/visitor-manage/VisitorController.ts:92</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getVisitorLocationList" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Visitor<wbr/>Location<wbr/>List</span><a href="#getVisitorLocationList" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getVisitorLocationList.getVisitorLocationList-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Visitor<wbr/>Location<wbr/>List</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span><a href="#getVisitorLocationList.getVisitorLocationList-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">params</span>: <span class="tsd-signature-type">IGeneralDoc</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L34">modules/visitor-manage/VisitorController.ts:34</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getVisitorSessionHistoryList" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Visitor<wbr/>Session<wbr/>History<wbr/>List</span><a href="#getVisitorSessionHistoryList" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getVisitorSessionHistoryList.getVisitorSessionHistoryList-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Visitor<wbr/>Session<wbr/>History<wbr/>List</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">IVisitorSessionResponse</span><span class="tsd-signature-symbol">&gt;</span><a href="#getVisitorSessionHistoryList.getVisitorSessionHistoryList-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">params</span>: <span class="tsd-signature-type">any</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">IVisitorSessionResponse</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L156">modules/visitor-manage/VisitorController.ts:156</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getVisitorSessionList" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Visitor<wbr/>Session<wbr/>List</span><a href="#getVisitorSessionList" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getVisitorSessionList.getVisitorSessionList-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Visitor<wbr/>Session<wbr/>List</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">IVisitorSessionResponse</span><span class="tsd-signature-symbol">&gt;</span><a href="#getVisitorSessionList.getVisitorSessionList-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><hr>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">params</span>: <span class="tsd-signature-type">any</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">IVisitorSessionResponse</span><span class="tsd-signature-symbol">&gt;</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L150">modules/visitor-manage/VisitorController.ts:150</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getVisitorSessionListStatistic" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Visitor<wbr/>Session<wbr/>List<wbr/>Statistic</span><a href="#getVisitorSessionListStatistic" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getVisitorSessionListStatistic.getVisitorSessionListStatistic-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Visitor<wbr/>Session<wbr/>List<wbr/>Statistic</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">IVisitorSessionResponse</span><span class="tsd-signature-symbol">&gt;</span><a href="#getVisitorSessionListStatistic.getVisitorSessionListStatistic-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">params</span>: <span class="tsd-signature-type">any</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">IVisitorSessionResponse</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L162">modules/visitor-manage/VisitorController.ts:162</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="updateVisitor" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>update<wbr/>Visitor</span><a href="#updateVisitor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="updateVisitor.updateVisitor-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">update<wbr/>Visitor</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#updateVisitor.updateVisitor-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">IIotVisitorInfor</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L104">modules/visitor-manage/VisitorController.ts:104</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="updateVisitorCard" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>update<wbr/>Visitor<wbr/>Card</span><a href="#updateVisitorCard" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="updateVisitorCard.updateVisitorCard-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">update<wbr/>Visitor<wbr/>Card</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#updateVisitorCard.updateVisitorCard-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">IIotVisitorCard</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L77">modules/visitor-manage/VisitorController.ts:77</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="updateVisitorCreatedAll" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>update<wbr/>Visitor<wbr/>Created<wbr/>All</span><a href="#updateVisitorCreatedAll" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="updateVisitorCreatedAll.updateVisitorCreatedAll-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">update<wbr/>Visitor<wbr/>Created<wbr/>All</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">QueryResultRow</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#updateVisitorCreatedAll.updateVisitorCreatedAll-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">QueryResultRow</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L128">modules/visitor-manage/VisitorController.ts:128</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="updateVisitorDeleted" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>update<wbr/>Visitor<wbr/>Deleted</span><a href="#updateVisitorDeleted" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="updateVisitorDeleted.updateVisitorDeleted-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">update<wbr/>Visitor<wbr/>Deleted</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">QueryResultRow</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#updateVisitorDeleted.updateVisitorDeleted-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">IIotVisitorInfor</span><span class="tsd-signature-symbol">[]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">QueryResultRow</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L134">modules/visitor-manage/VisitorController.ts:134</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="updateVisitorLocation" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>update<wbr/>Visitor<wbr/>Location</span><a href="#updateVisitorLocation" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="updateVisitorLocation.updateVisitorLocation-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">update<wbr/>Visitor<wbr/>Location</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#updateVisitorLocation.updateVisitorLocation-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">IIotVisitorLocation</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L46">modules/visitor-manage/VisitorController.ts:46</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="updateVisitorSession" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>update<wbr/>Visitor<wbr/>Session</span><a href="#updateVisitorSession" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="updateVisitorSession.updateVisitorSession-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">update<wbr/>Visitor<wbr/>Session</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#updateVisitorSession.updateVisitorSession-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">IIotVisitorSession</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L174">modules/visitor-manage/VisitorController.ts:174</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="updateVisitorSync" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>update<wbr/>Visitor<wbr/>Sync</span><a href="#updateVisitorSync" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="updateVisitorSync.updateVisitorSync-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">update<wbr/>Visitor<wbr/>Sync</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">QueryResultRow</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#updateVisitorSync.updateVisitorSync-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">IIotVisitorInfor</span><span class="tsd-signature-symbol">[]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">QueryResultRow</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L110">modules/visitor-manage/VisitorController.ts:110</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="updateVisitorUpdated" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>update<wbr/>Visitor<wbr/>Updated</span><a href="#updateVisitorUpdated" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="updateVisitorUpdated.updateVisitorUpdated-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">update<wbr/>Visitor<wbr/>Updated</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span>, <span class="tsd-kind-parameter">body</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">QueryResultRow</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#updateVisitorUpdated.updateVisitorUpdated-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li><li><span><span class="tsd-kind-parameter">body</span>: <span class="tsd-signature-type">IIotVisitorInfor</span><span class="tsd-signature-symbol">[]</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">QueryResultRow</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L116">modules/visitor-manage/VisitorController.ts:116</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="updateVisitorUpdatedAll" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>update<wbr/>Visitor<wbr/>Updated<wbr/>All</span><a href="#updateVisitorUpdatedAll" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="updateVisitorUpdatedAll.updateVisitorUpdatedAll-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">update<wbr/>Visitor<wbr/>Updated<wbr/>All</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">user</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">QueryResultRow</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#updateVisitorUpdatedAll.updateVisitorUpdatedAll-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">user</span>: <span class="tsd-signature-type">ICurrentUser </span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">QueryResultRow</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/VIIS-IOT/iot-backend-typescript/blob/1b251ac3bd879604f2ced8aa052c525fdcdf06fa/src/modules/visitor-manage/VisitorController.ts#L122">modules/visitor-manage/VisitorController.ts:122</a></li></ul></aside></li></ul></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><span class="settings-label">Member Visibility</span><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li></ul></div><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="tsd-otp-Constructors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Constructors</summary><div><a href="#constructor" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="tsd-otp-Methods"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Methods</summary><div><a href="#createVisitor" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>create<wbr/>Visitor</span></a><a href="#createVisitorCard" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>create<wbr/>Visitor<wbr/>Card</span></a><a href="#createVisitorLocation" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>create<wbr/>Visitor<wbr/>Location</span></a><a href="#createVisitorSession" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>create<wbr/>Visitor<wbr/>Session</span></a><a href="#deleteVisitor" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete<wbr/>Visitor</span></a><a href="#deleteVisitorCard" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete<wbr/>Visitor<wbr/>Card</span></a><a href="#deleteVisitorLocation" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete<wbr/>Visitor<wbr/>Location</span></a><a href="#deleteVisitorSession" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>delete<wbr/>Visitor<wbr/>Session</span></a><a href="#getVisitorCardList" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Visitor<wbr/>Card<wbr/>List</span></a><a href="#getVisitorFreeCardList" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Visitor<wbr/>Free<wbr/>Card<wbr/>List</span></a><a href="#getVisitorList" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Visitor<wbr/>List</span></a><a href="#getVisitorLocationList" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Visitor<wbr/>Location<wbr/>List</span></a><a href="#getVisitorSessionHistoryList" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Visitor<wbr/>Session<wbr/>History<wbr/>List</span></a><a href="#getVisitorSessionList" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Visitor<wbr/>Session<wbr/>List</span></a><a href="#getVisitorSessionListStatistic" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Visitor<wbr/>Session<wbr/>List<wbr/>Statistic</span></a><a href="#updateVisitor" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor</span></a><a href="#updateVisitorCard" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Card</span></a><a href="#updateVisitorCreatedAll" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Created<wbr/>All</span></a><a href="#updateVisitorDeleted" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Deleted</span></a><a href="#updateVisitorLocation" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Location</span></a><a href="#updateVisitorSession" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Session</span></a><a href="#updateVisitorSync" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Sync</span></a><a href="#updateVisitorUpdated" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Updated</span></a><a href="#updateVisitorUpdatedAll" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>update<wbr/>Visitor<wbr/>Updated<wbr/>All</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../index.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1"></use></svg><span>VIIS Backend Documentation - v1.0.0</span></a><ul class="tsd-small-nested-navigation" id="tsd-nav-container" data-base=".."><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
