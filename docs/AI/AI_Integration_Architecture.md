# AI/LLM Integration Architecture Guide

## <PERSON>ụ<PERSON>

1. [<PERSON>ền Tảng <PERSON>](#nền-tảng-kiến-thức-cơ-bản)
2. [<PERSON><PERSON> Tích <PERSON>](#phân-tích-các-thành-phần)
3. [Ứng Dụng Cho IoT Backend](#ứng-dụng-cho-iot-backend)
4. [Implementation Roadmap](#implementation-roadmap)

---

## Nền Tảng Kiến Thức <PERSON>ơ Bản

### Vector Databases và Vai Trò Trong AI Applications

#### **Vector Database là gì?**

Vector database là specialized database được thiết kế để store, index và search high-dimensional vectors efficiently. Trong AI context, vectors này thường là embeddings - numerical representations của text, images, hoặc data khác.

```typescript
// Ví dụ vector embedding cho text
const textEmbedding = [0.1, -0.3, 0.8, 0.2, ...]; // 1536 dimensions cho OpenAI embeddings
const queryEmbedding = [0.2, -0.1, 0.7, 0.3, ...]; // Query vector

// Similarity search
const similarity = cosineSimilarity(textEmbedding, queryEmbedding);
```

#### **Tại Sao Cần Vector Search Cho RAG?**

**Traditional Search vs Vector Search:**

```typescript
// Traditional keyword search
const keywordResults = database.search("IoT device temperature sensor");
// Chỉ tìm exact matches hoặc keyword overlap

// Vector semantic search  
const vectorResults = vectorDB.search(embedQuery("IoT device temperature sensor"));
// Tìm semantically similar content: "thermal monitoring", "heat detection", etc.
```

**Benefits của Vector Search:**
- **Semantic Understanding**: Hiểu ý nghĩa thay vì chỉ keywords
- **Multilingual**: Hoạt động across languages
- **Fuzzy Matching**: Tìm related concepts
- **Scalability**: Efficient với large datasets

### Cách Vector Embeddings Hoạt Động

#### **Embedding Process:**

```mermaid
flowchart LR
    A[Raw Text] --> B[Tokenization]
    B --> C[Embedding Model]
    C --> D[Vector Representation]
    D --> E[Vector Database]
    
    F[Query Text] --> G[Same Embedding Model]
    G --> H[Query Vector]
    H --> I[Similarity Search]
    I --> E
    E --> J[Relevant Results]
```

#### **Code Example:**

```typescript
import { OpenAIEmbeddings } from 'langchain/embeddings/openai';

class EmbeddingService {
  private embeddings: OpenAIEmbeddings;

  constructor() {
    this.embeddings = new OpenAIEmbeddings({
      openAIApiKey: process.env.OPENAI_API_KEY,
      modelName: 'text-embedding-3-large', // 3072 dimensions
    });
  }

  async createEmbedding(text: string): Promise<number[]> {
    return await this.embeddings.embedQuery(text);
  }

  async createEmbeddings(texts: string[]): Promise<number[][]> {
    return await this.embeddings.embedDocuments(texts);
  }
}
```

### Kiến Trúc Tổng Quan AI-Powered Application

```mermaid
graph TB
    A[User Request] --> B[Application Layer]
    B --> C{Need External Data?}
    
    C -->|Yes| D[RAG Pipeline]
    C -->|No| E[Direct LLM Call]
    
    D --> F[Vector Search]
    F --> G[Vector Database]
    G --> H[Relevant Documents]
    H --> I[Context Assembly]
    I --> J[LLM with Context]
    
    E --> J
    J --> K[Response Processing]
    K --> L[User Response]
    
    M[Data Sources] --> N[ETL Pipeline]
    N --> O[Embedding Generation]
    O --> G
    
    P[Monitoring & Logging] --> B
    Q[Security Layer] --> B
```

#### **Core Components:**

1. **Application Layer**: Business logic, API endpoints
2. **RAG Pipeline**: Retrieval và augmentation logic
3. **Vector Database**: Semantic search capability
4. **LLM Integration**: Model API calls
5. **Data Pipeline**: ETL và embedding generation
6. **Infrastructure**: Monitoring, security, scaling

---

## Phân Tích Các Thành Phần

### LLM Models Có Sẵn

#### **Claude 3.5 Sonnet (Anthropic)**

**Capabilities:**
```typescript
interface ClaudeCapabilities {
  contextWindow: 200000; // tokens
  multimodal: ['text', 'images'];
  strengths: ['reasoning', 'coding', 'analysis', 'safety'];
  pricing: '$3-15 per 1M tokens';
  rateLimit: '4000 requests/minute';
}
```

**Best For IoT:**
- Complex device data analysis
- Code generation cho automation
- Safety-critical decision making
- Long document processing (manuals, logs)

**Limitations:**
- No real-time capabilities
- Limited multimodal (no audio/video)
- Geographic restrictions

#### **GPT-4o (OpenAI)**

**Capabilities:**
```typescript
interface GPT4oCapabilities {
  contextWindow: 128000; // tokens
  multimodal: ['text', 'images', 'audio'];
  strengths: ['general intelligence', 'creativity', 'multimodal'];
  pricing: '$5-15 per 1M tokens';
  rateLimit: '10000 requests/minute';
}
```

**Best For IoT:**
- Real-time interactions
- Multimodal device interfaces
- General-purpose automation
- Customer-facing applications

**Limitations:**
- Higher cost
- Less specialized reasoning
- API dependency

#### **Gemini 1.5 Pro (Google)**

**Capabilities:**
```typescript
interface GeminiCapabilities {
  contextWindow: 2000000; // tokens - massive!
  multimodal: ['text', 'images', 'video', 'audio'];
  strengths: ['long context', 'multimodal', 'cost-effective'];
  pricing: '$1.25-5 per 1M tokens';
  rateLimit: '2000 requests/minute';
}
```

**Best For IoT:**
- Massive log file analysis
- Video surveillance processing
- Cost-sensitive applications
- Research và development

**Limitations:**
- Newer ecosystem
- Less consistent performance
- Limited availability

### Frameworks/Tools Có Sẵn

#### **JavaScript/TypeScript Frameworks**

**LangChain.js:**
```typescript
import { ChatOpenAI } from 'langchain/chat_models/openai';
import { RetrievalQAChain } from 'langchain/chains';
import { PineconeStore } from 'langchain/vectorstores/pinecone';

class LangChainRAG {
  private llm: ChatOpenAI;
  private vectorStore: PineconeStore;

  constructor() {
    this.llm = new ChatOpenAI({
      modelName: 'gpt-4o',
      temperature: 0.2,
    });
  }

  async setupRAG() {
    const chain = RetrievalQAChain.fromLLM(
      this.llm,
      this.vectorStore.asRetriever()
    );
    return chain;
  }
}
```

**LlamaIndex.js:**
```typescript
import { OpenAI, VectorStoreIndex, Document } from 'llamaindex';

class LlamaIndexRAG {
  private llm: OpenAI;

  constructor() {
    this.llm = new OpenAI({ model: 'gpt-4o' });
  }

  async createIndex(documents: Document[]) {
    const index = await VectorStoreIndex.fromDocuments(documents);
    return index.asQueryEngine();
  }
}
```

**Vercel AI SDK:**
```typescript
import { openai } from '@ai-sdk/openai';
import { streamText, tool } from 'ai';
import { z } from 'zod';

export async function POST(req: Request) {
  const { messages } = await req.json();

  const result = await streamText({
    model: openai('gpt-4o'),
    messages,
    tools: {
      getDeviceStatus: tool({
        description: 'Get IoT device status',
        parameters: z.object({
          deviceId: z.string(),
        }),
        execute: async ({ deviceId }) => {
          return await getDeviceFromDB(deviceId);
        },
      }),
      controlDevice: tool({
        description: 'Control IoT device',
        parameters: z.object({
          deviceId: z.string(),
          action: z.enum(['start', 'stop', 'restart']),
          parameters: z.record(z.any()).optional(),
        }),
        execute: async ({ deviceId, action, parameters }) => {
          return await controlDeviceAction(deviceId, action, parameters);
        },
      }),
      getDeviceHistory: tool({
        description: 'Get device historical data',
        parameters: z.object({
          deviceId: z.string(),
          timeRange: z.enum(['1h', '24h', '7d', '30d']),
          metrics: z.array(z.string()).optional(),
        }),
        execute: async ({ deviceId, timeRange, metrics }) => {
          return await getDeviceHistoricalData(deviceId, timeRange, metrics);
        },
      }),
    },
  });

  return result.toAIStreamResponse();
}

// Integration với existing IoT services
async function getDeviceFromDB(deviceId: string) {
  const deviceService = Container.get(DeviceService);
  const device = await deviceService.getDeviceById(deviceId);

  return {
    id: device.name,
    type: device.device_type,
    status: device.status,
    location: device.location,
    lastSeen: device.last_seen,
    metrics: await deviceService.getLatestMetrics(deviceId)
  };
}

async function controlDeviceAction(deviceId: string, action: string, parameters?: any) {
  const thingsboardService = Container.get(ThingsboardAPIService);

  const rpcPayload = {
    method: action,
    params: parameters || {},
    timeout: 10000
  };

  return await thingsboardService.controlDeviceOneway(deviceId, rpcPayload);
}
```

**Framework Comparison:**

| Framework | Strengths | Best For | Learning Curve |
|-----------|-----------|----------|----------------|
| **LangChain.js** | Comprehensive, Many integrations | Complex workflows, RAG | Medium-High |
| **LlamaIndex.js** | Simple RAG, Good docs | Document Q&A, Knowledge bases | Medium |
| **Vercel AI SDK** | Streaming, Tool calling, Simple | Real-time chat, Function calling | Low-Medium |
| **Custom Implementation** | Full control, Optimized | Specific requirements | High |

#### **Vector Databases**

**Pinecone (Managed):**
```typescript
import { PineconeStore } from 'langchain/vectorstores/pinecone';
import { Pinecone } from '@pinecone-database/pinecone';

class PineconeService {
  private pinecone: Pinecone;

  constructor() {
    this.pinecone = new Pinecone({
      apiKey: process.env.PINECONE_API_KEY!,
    });
  }

  async createVectorStore(indexName: string) {
    const index = this.pinecone.Index(indexName);
    return await PineconeStore.fromExistingIndex(
      new OpenAIEmbeddings(),
      { pineconeIndex: index }
    );
  }
}
```

**pgvector (Self-hosted):**
```typescript
import { PGVectorStore } from 'langchain/vectorstores/pgvector';
import { Pool } from 'pg';

class PGVectorService {
  private pool: Pool;

  constructor() {
    this.pool = new Pool({
      connectionString: process.env.DATABASE_URL,
    });
  }

  async setupVectorStore() {
    // First, ensure pgvector extension is installed
    await this.pool.query('CREATE EXTENSION IF NOT EXISTS vector');

    return await PGVectorStore.initialize(
      new OpenAIEmbeddings(),
      {
        postgresConnectionOptions: {
          connectionString: process.env.DATABASE_URL,
        },
        tableName: 'iot_documents',
        columns: {
          idColumnName: 'id',
          vectorColumnName: 'embedding',
          contentColumnName: 'content',
          metadataColumnName: 'metadata',
        },
      }
    );
  }

  async createCustomTable() {
    // Custom table for IoT-specific data
    await this.pool.query(`
      CREATE TABLE IF NOT EXISTS iot_knowledge_base (
        id SERIAL PRIMARY KEY,
        device_type VARCHAR(100),
        issue_category VARCHAR(100),
        content TEXT,
        solution TEXT,
        embedding vector(1536),
        metadata JSONB,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);

    // Create index for faster similarity search
    await this.pool.query(`
      CREATE INDEX IF NOT EXISTS iot_knowledge_embedding_idx
      ON iot_knowledge_base
      USING ivfflat (embedding vector_cosine_ops)
      WITH (lists = 100)
    `);
  }
}
```

**Chroma (Open Source):**
```typescript
import { Chroma } from 'langchain/vectorstores/chroma';
import { ChromaClient } from 'chromadb';

class ChromaService {
  private client: ChromaClient;
  private collection: any;

  constructor() {
    this.client = new ChromaClient({
      path: process.env.CHROMA_URL || 'http://localhost:8000'
    });
  }

  async initialize() {
    this.collection = await this.client.getOrCreateCollection({
      name: 'iot-knowledge',
      metadata: { 'hnsw:space': 'cosine' }
    });

    return await Chroma.fromExistingCollection(
      new OpenAIEmbeddings(),
      {
        collectionName: 'iot-knowledge',
        url: process.env.CHROMA_URL,
      }
    );
  }

  async addIoTDocuments(documents: Array<{content: string, metadata: any}>) {
    const embeddings = await new OpenAIEmbeddings().embedDocuments(
      documents.map(d => d.content)
    );

    await this.collection.add({
      ids: documents.map((_, i) => `doc_${Date.now()}_${i}`),
      embeddings: embeddings,
      documents: documents.map(d => d.content),
      metadatas: documents.map(d => d.metadata)
    });
  }
}
```

**Vector Database Comparison:**

| Database | Deployment | Cost | Performance | IoT Suitability | Maintenance |
|----------|------------|------|-------------|-----------------|-------------|
| **Pinecone** | Managed | $$$ | Excellent | High (managed) | Low |
| **pgvector** | Self-hosted | $ | Good | High (existing PG) | Medium |
| **Chroma** | Self/Managed | $$ | Good | Medium | Medium |
| **Weaviate** | Self/Managed | $$ | Excellent | High (features) | Medium |
| **Qdrant** | Self/Managed | $$ | Excellent | High (performance) | Medium |

### Phần Developer Cần Tự Xây Dựng

#### **Business Logic Integration**

```typescript
// Custom IoT-specific RAG service
export class IoTRAGService {
  private vectorStore: VectorStore;
  private llm: ChatOpenAI;

  async queryDeviceIssues(deviceId: string, issue: string) {
    // 1. Get device context
    const deviceContext = await this.getDeviceContext(deviceId);
    
    // 2. Search similar issues
    const similarIssues = await this.vectorStore.similaritySearch(
      `${deviceContext.type} ${issue}`,
      5
    );
    
    // 3. Combine context
    const context = this.buildContext(deviceContext, similarIssues);
    
    // 4. Generate response
    return await this.llm.call([
      new SystemMessage(`You are an IoT expert. Context: ${context}`),
      new HumanMessage(issue)
    ]);
  }

  private async getDeviceContext(deviceId: string) {
    // Custom business logic
    return await this.deviceService.getDeviceWithHistory(deviceId);
  }
}
```

#### **Data Preprocessing Pipelines**

```typescript
export class IoTDataPipeline {
  constructor(
    private embeddingService: EmbeddingService,
    private vectorStore: VectorStore,
    private deviceService: DeviceService
  ) {}

  async processDeviceLogs(logs: DeviceLog[]) {
    // Batch processing for efficiency
    const batchSize = 100;
    const batches = this.chunkArray(logs, batchSize);

    for (const batch of batches) {
      await this.processBatch(batch);
    }
  }

  private async processBatch(logs: DeviceLog[]) {
    const documents = logs.map(log => ({
      content: this.formatLogForEmbedding(log),
      metadata: {
        deviceId: log.deviceId,
        timestamp: log.timestamp,
        severity: log.severity,
        type: 'device_log',
        deviceType: log.deviceType,
        location: log.location
      }
    }));

    // Generate embeddings in batch
    const embeddings = await this.embeddingService.createEmbeddings(
      documents.map(d => d.content)
    );

    // Store in vector database
    await this.vectorStore.addDocuments(documents, embeddings);
  }

  private formatLogForEmbedding(log: DeviceLog): string {
    return `Device ${log.deviceId} (${log.deviceType}): ${log.message}
            Status: ${log.status} Severity: ${log.severity}
            Location: ${log.location} Time: ${log.timestamp}
            Context: ${this.extractContext(log)}`;
  }

  private extractContext(log: DeviceLog): string {
    // Extract meaningful context from log
    const contexts = [];

    if (log.temperature) contexts.push(`Temperature: ${log.temperature}°C`);
    if (log.humidity) contexts.push(`Humidity: ${log.humidity}%`);
    if (log.batteryLevel) contexts.push(`Battery: ${log.batteryLevel}%`);
    if (log.signalStrength) contexts.push(`Signal: ${log.signalStrength}dBm`);

    return contexts.join(', ');
  }

  async processDeviceManuals(manuals: DeviceManual[]) {
    for (const manual of manuals) {
      // Split large documents into chunks
      const chunks = this.splitDocument(manual.content, 1000); // 1000 chars per chunk

      const documents = chunks.map((chunk, index) => ({
        content: chunk,
        metadata: {
          deviceType: manual.deviceType,
          section: manual.section,
          chunkIndex: index,
          type: 'manual',
          version: manual.version
        }
      }));

      await this.vectorStore.addDocuments(documents);
    }
  }

  private splitDocument(content: string, chunkSize: number): string[] {
    const chunks = [];
    const sentences = content.split(/[.!?]+/);
    let currentChunk = '';

    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length > chunkSize) {
        if (currentChunk) {
          chunks.push(currentChunk.trim());
          currentChunk = sentence;
        }
      } else {
        currentChunk += sentence + '. ';
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }

    return chunks;
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  // Scheduled data processing
  @Cron('0 */6 * * *') // Every 6 hours
  async scheduledDataSync() {
    console.log('Starting scheduled data sync...');

    try {
      // Process recent device logs
      const recentLogs = await this.deviceService.getRecentLogs(6); // Last 6 hours
      await this.processDeviceLogs(recentLogs);

      // Process any new device alerts
      const recentAlerts = await this.deviceService.getRecentAlerts(6);
      await this.processDeviceAlerts(recentAlerts);

      console.log('Scheduled data sync completed successfully');
    } catch (error) {
      console.error('Scheduled data sync failed:', error);
    }
  }

  async processDeviceAlerts(alerts: DeviceAlert[]) {
    const documents = alerts.map(alert => ({
      content: this.formatAlertForEmbedding(alert),
      metadata: {
        deviceId: alert.deviceId,
        alertType: alert.type,
        severity: alert.severity,
        timestamp: alert.timestamp,
        type: 'alert',
        resolved: alert.resolved
      }
    }));

    await this.vectorStore.addDocuments(documents);
  }

  private formatAlertForEmbedding(alert: DeviceAlert): string {
    return `Alert for device ${alert.deviceId}: ${alert.message}
            Type: ${alert.type} Severity: ${alert.severity}
            Conditions: ${JSON.stringify(alert.conditions)}
            Resolution: ${alert.resolution || 'Unresolved'}`;
  }
}
```

#### **Authentication và Security Layers**

```typescript
export class AISecurityService {
  private rateLimiter = new Map<string, { count: number, resetTime: number }>();
  private suspiciousPatterns = [
    /system\s*prompt/i,
    /ignore\s*previous/i,
    /forget\s*instructions/i,
    /act\s*as\s*if/i,
    /pretend\s*to\s*be/i
  ];

  async validateAIRequest(request: AIRequest, user: ICurrentUser): Promise<boolean> {
    // Rate limiting per user
    if (!await this.checkRateLimit(user.user_id)) {
      throw new HttpError(429, 'Rate limit exceeded. Please try again later.');
    }

    // Permission check based on user role
    if (!await this.checkPermissions(user, request.resourceType)) {
      throw new HttpError(403, 'Insufficient permissions for AI features');
    }

    // Content filtering for prompt injection
    if (await this.detectMaliciousContent(request.query)) {
      // Log security incident
      await this.logSecurityIncident(user, request, 'malicious_content');
      throw new HttpError(400, 'Request contains potentially harmful content');
    }

    // Check for sensitive data in query
    if (await this.containsSensitiveData(request.query)) {
      throw new HttpError(400, 'Request contains sensitive information');
    }

    return true;
  }

  private async checkRateLimit(userId: string): Promise<boolean> {
    const now = Date.now();
    const userLimit = this.rateLimiter.get(userId);

    // Reset if time window passed
    if (!userLimit || now > userLimit.resetTime) {
      this.rateLimiter.set(userId, {
        count: 1,
        resetTime: now + 60000 // 1 minute window
      });
      return true;
    }

    // Check if under limit (20 requests per minute for AI)
    if (userLimit.count >= 20) {
      return false;
    }

    userLimit.count++;
    return true;
  }

  private async checkPermissions(user: ICurrentUser, resourceType: string): Promise<boolean> {
    // Check if user has AI access
    if (!user.permissions?.includes('ai_access')) {
      return false;
    }

    // Check resource-specific permissions
    switch (resourceType) {
      case 'device_control':
        return user.permissions.includes('device_control');
      case 'system_analysis':
        return user.user_type === UserType.SYSTEM_USER;
      case 'data_export':
        return user.permissions.includes('data_export');
      default:
        return true;
    }
  }

  private async detectMaliciousContent(query: string): Promise<boolean> {
    // Check for prompt injection patterns
    for (const pattern of this.suspiciousPatterns) {
      if (pattern.test(query)) {
        return true;
      }
    }

    // Check for excessive length (potential DoS)
    if (query.length > 10000) {
      return true;
    }

    // Check for repeated patterns (potential spam)
    const words = query.split(/\s+/);
    const uniqueWords = new Set(words);
    if (words.length > 100 && uniqueWords.size / words.length < 0.3) {
      return true;
    }

    return false;
  }

  private async containsSensitiveData(query: string): Promise<boolean> {
    const sensitivePatterns = [
      /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/, // Credit card
      /\b\d{3}-\d{2}-\d{4}\b/, // SSN
      /password\s*[:=]\s*\S+/i, // Password
      /api[_-]?key\s*[:=]\s*\S+/i, // API key
      /token\s*[:=]\s*\S+/i // Token
    ];

    return sensitivePatterns.some(pattern => pattern.test(query));
  }

  async sanitizeAIResponse(response: string, user: ICurrentUser): Promise<string> {
    let sanitized = response;

    // Remove IP addresses
    sanitized = sanitized.replace(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g, '[IP_REDACTED]');

    // Remove email addresses
    sanitized = sanitized.replace(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, '[EMAIL_REDACTED]');

    // Remove potential API keys or tokens
    sanitized = sanitized.replace(/[a-zA-Z0-9]{32,}/g, '[TOKEN_REDACTED]');

    // Remove file paths
    sanitized = sanitized.replace(/[a-zA-Z]:\\[^\s]+/g, '[PATH_REDACTED]');
    sanitized = sanitized.replace(/\/[a-zA-Z0-9_\-\/\.]+/g, '[PATH_REDACTED]');

    // Customer-specific data redaction
    if (user.customer_id) {
      // Don't expose other customers' data
      sanitized = await this.redactOtherCustomerData(sanitized, user.customer_id);
    }

    return sanitized;
  }

  private async redactOtherCustomerData(response: string, currentCustomerId: string): Promise<string> {
    // This would need to be implemented based on your data structure
    // For now, a simple approach:
    const customerIdPattern = /customer[_-]?id\s*[:=]\s*([a-zA-Z0-9\-]+)/gi;

    return response.replace(customerIdPattern, (match, capturedId) => {
      if (capturedId !== currentCustomerId) {
        return match.replace(capturedId, '[CUSTOMER_ID_REDACTED]');
      }
      return match;
    });
  }

  async logSecurityIncident(user: ICurrentUser, request: AIRequest, incidentType: string) {
    const incident = {
      userId: user.user_id,
      customerId: user.customer_id,
      incidentType,
      query: request.query.substring(0, 500), // First 500 chars only
      timestamp: new Date(),
      userAgent: request.userAgent,
      ipAddress: request.ipAddress
    };

    // Log to security monitoring system
    console.warn('AI Security Incident:', incident);

    // Could also send to external security service
    // await this.securityMonitoringService.reportIncident(incident);
  }

  // Middleware for AI endpoints
  createSecurityMiddleware() {
    return async (req: any, res: any, next: any) => {
      try {
        const aiRequest: AIRequest = {
          query: req.body.query || req.body.message,
          resourceType: req.body.resourceType || 'general',
          userAgent: req.headers['user-agent'],
          ipAddress: req.ip
        };

        await this.validateAIRequest(aiRequest, req.user);
        next();
      } catch (error) {
        res.status(error.httpCode || 500).json({
          success: false,
          error: error.message
        });
      }
    };
  }
}

interface AIRequest {
  query: string;
  resourceType: string;
  userAgent?: string;
  ipAddress?: string;
}
```

---

## Ứng Dụng Cho IoT Backend

### Tích Hợp AI Vào Existing TypeScript/Node.js Codebase

#### **Architecture Integration Pattern**

```typescript
// Existing IoT service
@Service()
export class DeviceService {
  // Existing methods...
  
  // New AI-enhanced methods
  async getIntelligentDeviceInsights(deviceId: string) {
    const aiService = Container.get(IoTAIService);
    return await aiService.analyzeDevice(deviceId);
  }
}

// New AI service layer
@Service()
export class IoTAIService {
  constructor(
    private deviceService: DeviceService,
    private ragService: IoTRAGService,
    private llmService: LLMService
  ) {}

  async analyzeDevice(deviceId: string) {
    // Get device data
    const device = await this.deviceService.getDeviceWithHistory(deviceId);
    
    // AI analysis
    const insights = await this.ragService.queryDeviceIssues(
      deviceId, 
      'Analyze current status and predict issues'
    );
    
    return {
      device,
      aiInsights: insights,
      recommendations: await this.generateRecommendations(device, insights)
    };
  }
}
```

### Use Cases Phù Hợp Với IoT Platform

#### **1. Intelligent Device Management**

```typescript
export class IntelligentDeviceManager {
  constructor(
    private deviceService: DeviceService,
    private ragService: IoTRAGService,
    private llmService: LLMService,
    private vectorStore: VectorStore
  ) {}

  async diagnoseDeviceIssue(deviceId: string, symptoms: string[]) {
    const context = await this.buildDiagnosticContext(deviceId, symptoms);

    const systemPrompt = `You are an expert IoT device diagnostician with deep knowledge of industrial IoT systems.
    Analyze device data and symptoms to provide accurate diagnosis and actionable solutions.

    Always structure your response as:
    1. Primary Diagnosis
    2. Confidence Level (1-10)
    3. Root Cause Analysis
    4. Immediate Actions
    5. Long-term Recommendations
    6. Prevention Measures`;

    const userPrompt = `Device Analysis Request:

    Device Information:
    - ID: ${context.device.id}
    - Type: ${context.device.type}
    - Model: ${context.device.model}
    - Location: ${context.device.location}
    - Last Maintenance: ${context.device.lastMaintenance}

    Current Symptoms:
    ${symptoms.map(s => `- ${s}`).join('\n')}

    Recent Metrics:
    ${JSON.stringify(context.recentMetrics, null, 2)}

    Historical Issues:
    ${context.history.map(h => `- ${h.date}: ${h.issue} (Resolved: ${h.resolution})`).join('\n')}

    Similar Cases from Knowledge Base:
    ${context.similarCases.map(c => `- ${c.summary} (Success Rate: ${c.successRate}%)`).join('\n')}

    Environmental Factors:
    - Temperature: ${context.environment.temperature}°C
    - Humidity: ${context.environment.humidity}%
    - Power Quality: ${context.environment.powerQuality}
    `;

    const diagnosis = await this.llmService.query(systemPrompt, userPrompt);

    return {
      deviceId,
      diagnosis: diagnosis.content,
      confidence: this.calculateConfidence(context),
      recommendedActions: await this.extractActions(diagnosis.content),
      estimatedResolutionTime: this.estimateResolutionTime(context, diagnosis.content),
      requiredSkills: this.extractRequiredSkills(diagnosis.content),
      urgency: this.calculateUrgency(symptoms, context)
    };
  }

  private async buildDiagnosticContext(deviceId: string, symptoms: string[]) {
    // Get comprehensive device information
    const device = await this.deviceService.getDeviceWithFullContext(deviceId);

    // Get recent metrics and trends
    const recentMetrics = await this.deviceService.getRecentMetrics(deviceId, '24h');

    // Get historical issues
    const history = await this.deviceService.getDeviceIssueHistory(deviceId, 30); // Last 30 days

    // Search for similar cases in knowledge base
    const searchQuery = `${device.type} ${symptoms.join(' ')} troubleshooting`;
    const similarCases = await this.vectorStore.similaritySearch(searchQuery, 5);

    // Get environmental context
    const environment = await this.deviceService.getEnvironmentalContext(deviceId);

    return {
      device,
      recentMetrics,
      history,
      similarCases: similarCases.map(case_ => ({
        summary: case_.pageContent,
        successRate: case_.metadata.successRate || 85,
        lastUsed: case_.metadata.lastUsed
      })),
      environment
    };
  }

  private calculateConfidence(context: any): number {
    let confidence = 50; // Base confidence

    // Increase confidence based on available data
    if (context.history.length > 0) confidence += 15;
    if (context.similarCases.length >= 3) confidence += 20;
    if (context.recentMetrics.completeness > 0.8) confidence += 15;

    return Math.min(confidence, 95); // Cap at 95%
  }

  private async extractActions(diagnosisText: string): Promise<string[]> {
    const actionPrompt = `Extract specific, actionable steps from this diagnosis.
    Return only the action items as a JSON array of strings.

    Diagnosis: ${diagnosisText}`;

    const response = await this.llmService.query('', actionPrompt);

    try {
      return JSON.parse(response.content);
    } catch {
      // Fallback: extract bullet points
      const lines = diagnosisText.split('\n');
      return lines
        .filter(line => line.trim().match(/^[-*•]\s+/))
        .map(line => line.replace(/^[-*•]\s+/, '').trim());
    }
  }

  private estimateResolutionTime(context: any, diagnosis: string): string {
    // Simple heuristic based on issue complexity
    const complexityKeywords = ['replace', 'rewire', 'reprogram', 'hardware'];
    const simpleKeywords = ['restart', 'reset', 'recalibrate', 'clean'];

    const hasComplexKeywords = complexityKeywords.some(keyword =>
      diagnosis.toLowerCase().includes(keyword)
    );
    const hasSimpleKeywords = simpleKeywords.some(keyword =>
      diagnosis.toLowerCase().includes(keyword)
    );

    if (hasComplexKeywords) return '4-8 hours';
    if (hasSimpleKeywords) return '15-30 minutes';
    return '1-2 hours';
  }

  private extractRequiredSkills(diagnosis: string): string[] {
    const skillMap = {
      'electrical': ['wiring', 'voltage', 'current', 'electrical'],
      'mechanical': ['mechanical', 'motor', 'bearing', 'vibration'],
      'software': ['firmware', 'software', 'programming', 'configuration'],
      'network': ['network', 'connectivity', 'protocol', 'communication']
    };

    const requiredSkills = [];
    const lowerDiagnosis = diagnosis.toLowerCase();

    for (const [skill, keywords] of Object.entries(skillMap)) {
      if (keywords.some(keyword => lowerDiagnosis.includes(keyword))) {
        requiredSkills.push(skill);
      }
    }

    return requiredSkills.length > 0 ? requiredSkills : ['general'];
  }

  private calculateUrgency(symptoms: string[], context: any): 'low' | 'medium' | 'high' | 'critical' {
    const criticalKeywords = ['fire', 'smoke', 'explosion', 'safety'];
    const highKeywords = ['offline', 'failure', 'error', 'alarm'];
    const mediumKeywords = ['warning', 'degraded', 'slow'];

    const allText = symptoms.join(' ').toLowerCase();

    if (criticalKeywords.some(keyword => allText.includes(keyword))) return 'critical';
    if (highKeywords.some(keyword => allText.includes(keyword))) return 'high';
    if (mediumKeywords.some(keyword => allText.includes(keyword))) return 'medium';

    return 'low';
  }

  // Batch diagnosis for multiple devices
  async diagnoseBatch(deviceIds: string[]): Promise<any[]> {
    const results = await Promise.allSettled(
      deviceIds.map(async deviceId => {
        // Auto-detect symptoms from recent data
        const symptoms = await this.autoDetectSymptoms(deviceId);
        return await this.diagnoseDeviceIssue(deviceId, symptoms);
      })
    );

    return results.map((result, index) => ({
      deviceId: deviceIds[index],
      success: result.status === 'fulfilled',
      data: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason.message : null
    }));
  }

  private async autoDetectSymptoms(deviceId: string): Promise<string[]> {
    const metrics = await this.deviceService.getRecentMetrics(deviceId, '1h');
    const symptoms = [];

    // Analyze metrics for anomalies
    if (metrics.temperature && metrics.temperature > metrics.normalRange.temperature.max) {
      symptoms.push('High temperature detected');
    }

    if (metrics.connectivity && metrics.connectivity < 0.8) {
      symptoms.push('Poor connectivity');
    }

    if (metrics.batteryLevel && metrics.batteryLevel < 20) {
      symptoms.push('Low battery level');
    }

    if (metrics.errorRate && metrics.errorRate > 0.1) {
      symptoms.push('High error rate');
    }

    return symptoms.length > 0 ? symptoms : ['General performance check requested'];
  }
}
```

#### **2. Predictive Maintenance**

```typescript
export class PredictiveMaintenanceAI {
  async predictMaintenanceNeeds(deviceId: string) {
    // Get device metrics
    const metrics = await this.getDeviceMetrics(deviceId);
    
    // Search similar patterns
    const similarPatterns = await this.vectorStore.similaritySearch(
      this.formatMetricsForSearch(metrics),
      10
    );

    const prediction = await this.llm.call([
      new SystemMessage(`Analyze IoT device metrics and predict maintenance needs based on historical patterns.`),
      new HumanMessage(`Current Metrics: ${JSON.stringify(metrics)}
                        Similar Historical Patterns: ${JSON.stringify(similarPatterns)}
                        Predict: 1) Likelihood of failure in next 30 days
                                2) Recommended maintenance actions
                                3) Priority level`)
    ]);

    return this.parsePrediction(prediction.content);
  }
}
```

#### **3. Intelligent Alerts**

```typescript
export class IntelligentAlertSystem {
  async processAlert(alert: DeviceAlert) {
    // Enrich alert with AI analysis
    const enrichedAlert = await this.enrichAlert(alert);
    
    // Determine severity and actions
    const analysis = await this.analyzeAlert(enrichedAlert);
    
    // Generate human-readable explanation
    const explanation = await this.generateExplanation(alert, analysis);
    
    return {
      ...alert,
      aiAnalysis: analysis,
      explanation: explanation,
      recommendedActions: analysis.actions,
      priority: analysis.priority
    };
  }

  private async enrichAlert(alert: DeviceAlert) {
    // Get related context
    const deviceHistory = await this.getDeviceHistory(alert.deviceId);
    const similarAlerts = await this.findSimilarAlerts(alert);
    
    return {
      ...alert,
      context: {
        deviceHistory,
        similarAlerts,
        environmentalFactors: await this.getEnvironmentalContext(alert)
      }
    };
  }
}
```

### Architecture Patterns Cho Enterprise IoT Applications

#### **Microservices Pattern với AI**

```mermaid
graph TB
    A[API Gateway] --> B[Device Management Service]
    A --> C[AI Analysis Service]
    A --> D[Alert Management Service]
    
    C --> E[Vector Database]
    C --> F[LLM Service]
    C --> G[Embedding Service]
    
    B --> H[Device Database]
    D --> I[Alert Database]
    
    J[Data Pipeline] --> E
    J --> H
    J --> I
    
    K[Monitoring] --> A
    K --> B
    K --> C
    K --> D
```

#### **Event-Driven AI Processing**

```typescript
// Event-driven AI processing
@EventSubscriber()
export class AIEventProcessor {
  @On(DeviceDataReceived)
  async processDeviceData(event: DeviceDataReceived) {
    // Async AI processing
    await this.queueAIAnalysis(event.deviceId, event.data);
  }

  @On(AlertGenerated)
  async enhanceAlert(event: AlertGenerated) {
    // Real-time alert enhancement
    const enhancedAlert = await this.aiService.processAlert(event.alert);
    await this.eventBus.publish(new AlertEnhanced(enhancedAlert));
  }

  private async queueAIAnalysis(deviceId: string, data: any) {
    // Use queue for heavy AI processing
    await this.queue.add('ai-analysis', {
      deviceId,
      data,
      timestamp: new Date()
    });
  }
}
```

---

## Implementation Roadmap

### Phase 1: Foundation (Weeks 1-4)

#### **Week 1-2: Setup Infrastructure**

```bash
# Install dependencies
npm install langchain @langchain/openai @pinecone-database/pinecone
npm install @ai-sdk/openai ai
npm install @types/node dotenv

# Setup environment
echo "OPENAI_API_KEY=your_key" >> .env
echo "PINECONE_API_KEY=your_key" >> .env
echo "PINECONE_ENVIRONMENT=your_env" >> .env
```

**Basic AI Service Setup:**
```typescript
// src/services/ai/AIService.ts
@Service()
export class AIService {
  private llm: ChatOpenAI;
  private embeddings: OpenAIEmbeddings;

  constructor() {
    this.llm = new ChatOpenAI({
      modelName: 'gpt-4o',
      temperature: 0.2,
      openAIApiKey: process.env.OPENAI_API_KEY,
    });

    this.embeddings = new OpenAIEmbeddings({
      openAIApiKey: process.env.OPENAI_API_KEY,
    });
  }

  async simpleQuery(question: string): Promise<string> {
    const response = await this.llm.call([
      new HumanMessage(question)
    ]);
    return response.content;
  }
}
```

#### **Week 3-4: Basic RAG Implementation**

```typescript
// src/services/ai/RAGService.ts
@Service()
export class RAGService {
  private vectorStore: PineconeStore;

  async initialize() {
    const pinecone = new Pinecone({
      apiKey: process.env.PINECONE_API_KEY!,
    });

    const index = pinecone.Index('iot-knowledge');
    
    this.vectorStore = await PineconeStore.fromExistingIndex(
      new OpenAIEmbeddings(),
      { pineconeIndex: index }
    );
  }

  async queryWithContext(question: string): Promise<string> {
    // Search relevant documents
    const relevantDocs = await this.vectorStore.similaritySearch(question, 5);
    
    // Build context
    const context = relevantDocs.map(doc => doc.pageContent).join('\n\n');
    
    // Query LLM with context
    const llm = new ChatOpenAI({ modelName: 'gpt-4o' });
    const response = await llm.call([
      new SystemMessage(`Answer based on the following context: ${context}`),
      new HumanMessage(question)
    ]);

    return response.content;
  }
}
```

### Phase 2: Integration (Weeks 5-8)

#### **Week 5-6: Integrate với Existing Services**

```typescript
// Extend existing controllers
@JsonController('/devices')
export class DeviceController {
  constructor(
    private deviceService: DeviceService,
    private aiService: AIService // New AI service
  ) {}

  // Existing endpoints...

  @Get('/:id/ai-insights')
  @Authorized([UserType.VIIS_IOT_USER])
  async getAIInsights(
    @Param('id') deviceId: string,
    @CurrentUser() user: ICurrentUser
  ) {
    const insights = await this.aiService.analyzeDevice(deviceId);
    return {
      success: true,
      data: insights
    };
  }

  @Post('/:id/ask')
  @Authorized([UserType.VIIS_IOT_USER])
  async askAboutDevice(
    @Param('id') deviceId: string,
    @Body() body: { question: string },
    @CurrentUser() user: ICurrentUser
  ) {
    const answer = await this.aiService.answerDeviceQuestion(deviceId, body.question);
    return {
      success: true,
      data: { answer }
    };
  }
}
```

#### **Week 7-8: Data Pipeline Setup**

```typescript
// src/services/ai/DataPipelineService.ts
@Service()
export class DataPipelineService {
  constructor(
    private ragService: RAGService,
    private deviceService: DeviceService
  ) {}

  @Cron('0 2 * * *') // Daily at 2 AM
  async syncDeviceDataToVector() {
    console.log('Starting daily vector sync...');
    
    // Get recent device data
    const recentData = await this.deviceService.getRecentDeviceData(24); // Last 24 hours
    
    // Process and embed
    const documents = this.formatDataForEmbedding(recentData);
    await this.ragService.addDocuments(documents);
    
    console.log(`Synced ${documents.length} documents to vector store`);
  }

  private formatDataForEmbedding(data: any[]): Document[] {
    return data.map(item => ({
      pageContent: `Device ${item.deviceId}: ${item.description}`,
      metadata: {
        deviceId: item.deviceId,
        timestamp: item.timestamp,
        type: 'device_data'
      }
    }));
  }
}
```

### Phase 3: Advanced Features (Weeks 9-12)

#### **Week 9-10: Intelligent Alerts**

```typescript
// src/services/ai/IntelligentAlertService.ts
@Service()
export class IntelligentAlertService {
  async processAlert(alert: any) {
    // Get context
    const context = await this.buildAlertContext(alert);
    
    // AI analysis
    const analysis = await this.analyzeAlert(alert, context);
    
    // Generate recommendations
    const recommendations = await this.generateRecommendations(analysis);
    
    return {
      ...alert,
      aiAnalysis: analysis,
      recommendations,
      priority: this.calculatePriority(analysis)
    };
  }
}
```

#### **Week 11-12: Monitoring và Optimization**

```typescript
// src/services/ai/AIMonitoringService.ts
@Service()
export class AIMonitoringService {
  async logAIUsage(request: any, response: any, duration: number) {
    await this.metricsService.record({
      type: 'ai_request',
      duration,
      tokens: response.usage?.total_tokens,
      model: request.model,
      success: response.success
    });
  }

  async getAIMetrics() {
    return {
      totalRequests: await this.getTotalRequests(),
      averageResponseTime: await this.getAverageResponseTime(),
      tokenUsage: await this.getTokenUsage(),
      errorRate: await this.getErrorRate()
    };
  }
}
```

### Cách Evaluate và Chọn Tools

#### **Evaluation Criteria:**

```typescript
interface ToolEvaluation {
  cost: number; // 1-10 scale
  performance: number;
  easeOfIntegration: number;
  scalability: number;
  maintenance: number;
  communitySupport: number;
}

const toolComparison = {
  vectorDatabases: {
    pinecone: { cost: 6, performance: 9, easeOfIntegration: 9, scalability: 9, maintenance: 9, communitySupport: 8 },
    pgvector: { cost: 9, performance: 7, easeOfIntegration: 6, scalability: 7, maintenance: 5, communitySupport: 7 },
    weaviate: { cost: 7, performance: 8, easeOfIntegration: 7, scalability: 8, maintenance: 7, communitySupport: 6 }
  },
  llmProviders: {
    openai: { cost: 6, performance: 9, easeOfIntegration: 9, scalability: 9, maintenance: 8, communitySupport: 9 },
    anthropic: { cost: 7, performance: 9, easeOfIntegration: 8, scalability: 8, maintenance: 8, communitySupport: 7 },
    google: { cost: 8, performance: 7, easeOfIntegration: 7, scalability: 8, maintenance: 7, communitySupport: 6 }
  }
};
```

### Best Practices Cho Production Deployment

#### **1. Error Handling và Fallbacks**

```typescript
export class RobustAIService {
  private primaryAI: LLMService;
  private fallbackAI: LLMService;
  private cache: Map<string, { response: string, timestamp: number }> = new Map();
  private circuitBreaker: CircuitBreaker;

  constructor() {
    this.primaryAI = new LLMService('gpt-4o');
    this.fallbackAI = new LLMService('gpt-4o-mini'); // Cheaper, faster fallback

    this.circuitBreaker = new CircuitBreaker({
      failureThreshold: 5,
      resetTimeout: 60000, // 1 minute
      monitoringPeriod: 300000 // 5 minutes
    });
  }

  async queryWithFallback(question: string, context?: any): Promise<AIResponse> {
    const cacheKey = this.generateCacheKey(question, context);

    // Check cache first
    const cached = this.getCachedResponse(cacheKey);
    if (cached) {
      return {
        content: cached.response,
        source: 'cache',
        confidence: 0.9,
        responseTime: 0
      };
    }

    const startTime = Date.now();

    try {
      // Check circuit breaker
      if (this.circuitBreaker.isOpen()) {
        throw new Error('Circuit breaker is open');
      }

      // Try primary AI service
      const response = await this.primaryAI.query(question, context);

      // Cache successful response
      this.setCachedResponse(cacheKey, response.content);

      this.circuitBreaker.recordSuccess();

      return {
        ...response,
        source: 'primary',
        responseTime: Date.now() - startTime
      };

    } catch (error) {
      console.warn('Primary AI failed, trying fallback:', error);
      this.circuitBreaker.recordFailure();

      try {
        // Fallback to secondary service
        const fallbackResponse = await this.fallbackAI.query(question, context);

        return {
          ...fallbackResponse,
          source: 'fallback',
          responseTime: Date.now() - startTime,
          confidence: Math.max(0, (fallbackResponse.confidence || 0.8) - 0.1) // Slightly lower confidence
        };

      } catch (fallbackError) {
        console.error('All AI services failed:', fallbackError);

        // Try to find similar cached response
        const similarCached = this.findSimilarCachedResponse(question);
        if (similarCached) {
          return {
            content: `Based on similar previous queries: ${similarCached.response}`,
            source: 'similar_cache',
            confidence: 0.6,
            responseTime: Date.now() - startTime
          };
        }

        // Return intelligent fallback based on question type
        return {
          content: this.generateIntelligentFallback(question),
          source: 'fallback_template',
          confidence: 0.3,
          responseTime: Date.now() - startTime
        };
      }
    }
  }

  private getCachedResponse(key: string): { response: string } | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < 3600000) { // 1 hour
      return cached;
    }
    return null;
  }

  private setCachedResponse(key: string, response: string): void {
    this.cache.set(key, {
      response,
      timestamp: Date.now()
    });

    // Cleanup old cache entries
    if (this.cache.size > 1000) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
  }

  private generateCacheKey(question: string, context?: any): string {
    const contextStr = context ? JSON.stringify(context) : '';
    return require('crypto')
      .createHash('md5')
      .update(question + contextStr)
      .digest('hex');
  }

  private findSimilarCachedResponse(question: string): { response: string } | null {
    // Simple similarity check - in production, use proper similarity search
    const questionWords = question.toLowerCase().split(/\s+/);

    for (const [key, cached] of this.cache.entries()) {
      // This is simplified - you'd want to store original questions for better matching
      const similarity = this.calculateSimpleSimilarity(questionWords, key);
      if (similarity > 0.7) {
        return cached;
      }
    }

    return null;
  }

  private calculateSimpleSimilarity(words1: string[], text2: string): number {
    const words2 = text2.toLowerCase().split(/\s+/);
    const intersection = words1.filter(word => words2.includes(word));
    return intersection.length / Math.max(words1.length, words2.length);
  }

  private generateIntelligentFallback(question: string): string {
    const lowerQuestion = question.toLowerCase();

    if (lowerQuestion.includes('device') && lowerQuestion.includes('status')) {
      return "I'm currently unable to check device status. Please try refreshing the page or contact support if the issue persists.";
    }

    if (lowerQuestion.includes('troubleshoot') || lowerQuestion.includes('problem')) {
      return "I'm temporarily unable to provide troubleshooting assistance. Please check our documentation or contact technical support for immediate help.";
    }

    if (lowerQuestion.includes('how') || lowerQuestion.includes('what')) {
      return "I'm currently experiencing technical difficulties. Please refer to our help documentation or try again in a few minutes.";
    }

    return "I'm sorry, I'm currently unable to process your request due to technical issues. Please try again later or contact support if this is urgent.";
  }
}

class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';

  constructor(
    private config: {
      failureThreshold: number;
      resetTimeout: number;
      monitoringPeriod: number;
    }
  ) {}

  isOpen(): boolean {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime > this.config.resetTimeout) {
        this.state = 'half-open';
        return false;
      }
      return true;
    }
    return false;
  }

  recordSuccess(): void {
    this.failures = 0;
    this.state = 'closed';
  }

  recordFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.config.failureThreshold) {
      this.state = 'open';
    }
  }
}

interface AIResponse {
  content: string;
  source: 'primary' | 'fallback' | 'cache' | 'similar_cache' | 'fallback_template';
  confidence: number;
  responseTime: number;
}
```

#### **2. Rate Limiting và Cost Control**

```typescript
export class AIRateLimiter {
  private userLimits = new Map<string, { count: number, resetTime: number }>();

  async checkLimit(userId: string): Promise<boolean> {
    const limit = this.userLimits.get(userId);
    const now = Date.now();

    if (!limit || now > limit.resetTime) {
      this.userLimits.set(userId, {
        count: 1,
        resetTime: now + 60000 // 1 minute
      });
      return true;
    }

    if (limit.count >= 10) { // 10 requests per minute
      return false;
    }

    limit.count++;
    return true;
  }
}
```

#### **3. Caching Strategy**

```typescript
export class AICacheService {
  private cache = new Map<string, { response: string, timestamp: number }>();

  async getCachedResponse(query: string): Promise<string | null> {
    const cached = this.cache.get(this.hashQuery(query));
    
    if (cached && Date.now() - cached.timestamp < 3600000) { // 1 hour
      return cached.response;
    }
    
    return null;
  }

  async setCachedResponse(query: string, response: string): Promise<void> {
    this.cache.set(this.hashQuery(query), {
      response,
      timestamp: Date.now()
    });
  }

  private hashQuery(query: string): string {
    return require('crypto').createHash('md5').update(query).digest('hex');
  }
}
```

---

## Kết Luận

AI integration vào IoT backend là một journey phức tạp nhưng mang lại giá trị to lớn. Thông qua guide này, chúng ta đã khám phá:

### **Key Takeaways:**

1. **Foundation First**: Vector databases và embeddings là nền tảng cho intelligent applications
2. **Hybrid Approach**: Combine existing LLM capabilities với custom business logic
3. **Security Critical**: AI systems cần robust security và monitoring
4. **Incremental Development**: Start simple, build incrementally, scale gradually

### **Success Factors:**

1. **Start Simple**: Begin với basic LLM integration trước khi move to complex RAG
2. **Focus on Value**: Prioritize use cases với clear business impact và ROI
3. **Build Incrementally**: Add AI capabilities gradually to existing system
4. **Monitor Everything**: Track performance, costs, user satisfaction, và security
5. **Plan for Scale**: Design architecture có thể handle growth và increased usage

### **Next Steps:**

1. **Immediate (Week 1-2)**:
   - Setup development environment với OpenAI API
   - Implement basic AI service integration
   - Create simple device query functionality

2. **Short-term (Month 1-2)**:
   - Deploy vector database (recommend starting với pgvector)
   - Implement RAG pipeline cho device documentation
   - Add AI-powered device diagnostics

3. **Medium-term (Month 3-6)**:
   - Expand to predictive maintenance
   - Implement intelligent alerting
   - Add multi-modal capabilities (images, sensor data)

4. **Long-term (6+ months)**:
   - Autonomous device management
   - Advanced analytics và insights
   - Custom model fine-tuning

### **Final Recommendations:**

**For Technical Teams:**
- Invest time trong understanding vector databases và embeddings
- Practice với different LLM providers để understand strengths/weaknesses
- Build robust error handling và fallback mechanisms từ đầu
- Implement comprehensive monitoring và logging

**For Business Teams:**
- Define clear success metrics cho AI initiatives
- Start với high-impact, low-risk use cases
- Budget cho both development và operational costs
- Plan cho user training và change management

**For Leadership:**
- AI is not magic - it requires proper planning và execution
- ROI comes from solving real business problems, not just using latest technology
- Security và compliance should be built-in, not added later
- Long-term success requires commitment to continuous learning và improvement

Với proper planning và execution, AI có thể transform IoT platform từ reactive system thành intelligent, proactive solution that anticipates problems, optimizes performance, và delivers exceptional user experiences.

---

## Additional Resources

### **Documentation Links:**
- [LangChain.js Documentation](https://js.langchain.com/)
- [OpenAI API Reference](https://platform.openai.com/docs)
- [Pinecone Vector Database](https://docs.pinecone.io/)
- [pgvector Extension](https://github.com/pgvector/pgvector)

### **Example Repositories:**
- [LangChain.js Examples](https://github.com/langchain-ai/langchainjs/tree/main/examples)
- [Vercel AI SDK Examples](https://github.com/vercel/ai/tree/main/examples)
- [OpenAI Cookbook](https://github.com/openai/openai-cookbook)

### **Monitoring và Analytics:**
- [LangSmith](https://smith.langchain.com/) - LangChain monitoring
- [Weights & Biases](https://wandb.ai/) - ML experiment tracking
- [Helicone](https://helicone.ai/) - LLM observability

### **Security Resources:**
- [OWASP AI Security Guidelines](https://owasp.org/www-project-ai-security-and-privacy-guide/)
- [AI Red Team Guidelines](https://learn.microsoft.com/en-us/azure/ai-services/openai/concepts/red-teaming)

*Last updated: December 2024*
