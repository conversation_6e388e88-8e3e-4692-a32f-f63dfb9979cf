# VIIS IoT Backend Development Guidelines

## Overview
This document provides comprehensive guidelines for developing and maintaining the VIIS IoT Backend TypeScript application.

## Project Structure

### Core Directories
- `src/modules/` - Feature modules organized by business domain
- `src/services/` - External service integrations and utilities
- `src/orm/` - Database entities, migrations, and data access
- `src/interfaces/` - TypeScript interface definitions
- `src/utils/` - Shared utility functions and helpers
- `src/loaders/` - Application initialization and bootstrapping

### Module Organization
Each module should follow this structure:
```
src/modules/{module-name}/
├── Controller.ts          # REST API endpoints
├── Service.ts            # Business logic
├── Validator.ts          # Input validation schemas
├── {Module}SQL.ts        # Raw SQL queries (if needed)
└── types.ts              # Module-specific types
```

## Coding Standards

### TypeScript Guidelines
1. **Strict Mode**: Always use strict TypeScript configuration
2. **Path Mapping**: Use `@app/*` alias instead of relative imports
3. **Interface Naming**: Prefix interfaces with `I` (e.g., `ICurrentUser`)
4. **Type Safety**: Avoid `any` type, use proper type definitions

### Code Style
- **Indentation**: 2 spaces
- **Quotes**: Single quotes for strings
- **Semicolons**: Always use semicolons
- **Trailing Commas**: Use trailing commas in objects and arrays

### Naming Conventions
- **Classes**: PascalCase (e.g., `DeviceController`)
- **Methods**: camelCase (e.g., `getDeviceData`)
- **Variables**: camelCase (e.g., `deviceId`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_RETRY_COUNT`)
- **Files**: camelCase or kebab-case

## Architecture Patterns

### Controller Pattern
```typescript
@JsonController('/devices')
export class DeviceController {
  private deviceService: DeviceService;
  
  constructor() {
    this.deviceService = Container.get(DeviceService);
  }

  @Get('/:id')
  @Authorized([UserType.VIIS_IOT_USER])
  async getDevice(
    @Param('id') id: string,
    @CurrentUser() user: ICurrentUser
  ) {
    return await this.deviceService.getDevice(user, id);
  }
}
```

### Service Pattern
```typescript
@Service()
export class DeviceService {
  private deviceRepo = AppDataSource.getRepository(IoTDevice);
  private cacheService = new RedisService();

  async getDevice(user: ICurrentUser, deviceId: string) {
    try {
      // Check cache first
      const cached = await this.cacheService.get(`device:${deviceId}`);
      if (cached) return cached;

      // Query database
      const device = await this.deviceRepo.findOne({
        where: { id: deviceId, tenantId: user.tenantId }
      });

      // Cache result
      await this.cacheService.set(`device:${deviceId}`, device, 3600);
      
      return device;
    } catch (error) {
      Logger.error('Error fetching device:', error);
      throw error;
    }
  }
}
```

## IoT-Specific Guidelines

### Device Management
1. **Device Identification**: Use consistent device ID format across all systems
2. **Telemetry Processing**: Always cache latest values in Redis
3. **Command Handling**: Validate commands before sending to devices
4. **Status Tracking**: Maintain device online/offline status

### MQTT Integration
- **Topic Structure**: `viis/things/v2/{deviceId}/{action}`
- **QoS Levels**: Use QoS 1 for important messages
- **Message Format**: JSON with timestamp and metadata
- **Error Handling**: Implement retry logic for failed messages

### Thingsboard Integration
- **Rule Chains**: Use standardized rule chain naming (`viis_iot_v2_*`)
- **Device Profiles**: Create reusable device profiles
- **Alarms**: Configure appropriate alarm conditions
- **Dashboards**: Maintain consistent dashboard layouts

## Database Guidelines

### Entity Design
```typescript
@Entity('iot_devices')
export class IoTDevice {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  tenantId: string;

  @Column({ name: 'device_name' })
  deviceName: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
```

### Migration Best Practices
1. **Descriptive Names**: Use clear migration names
2. **Rollback Support**: Always provide down migrations
3. **Data Safety**: Test migrations on staging first
4. **Indexing**: Add appropriate indexes for performance

## Security Guidelines

### Authentication
- **JWT Tokens**: Use secure JWT implementation
- **Token Expiry**: Set appropriate expiration times
- **Refresh Tokens**: Implement token refresh mechanism
- **Multi-Factor**: Support MFA for admin users

### Authorization
- **Role-Based Access**: Implement proper RBAC
- **Tenant Isolation**: Ensure data isolation between tenants
- **API Security**: Validate all input parameters
- **Rate Limiting**: Implement API rate limiting

## Testing Guidelines

### Unit Tests
```typescript
describe('DeviceService', () => {
  let service: DeviceService;
  
  beforeEach(() => {
    service = new DeviceService();
  });

  it('should get device by id', async () => {
    // Arrange
    const deviceId = 'test-device-id';
    const mockUser = { tenantId: 'tenant-1' };

    // Act
    const result = await service.getDevice(mockUser, deviceId);

    // Assert
    expect(result).toBeDefined();
    expect(result.id).toBe(deviceId);
  });
});
```

### Integration Tests
- Test database operations with test database
- Mock external service calls
- Test complete API workflows
- Verify error handling scenarios

## Performance Guidelines

### Caching Strategy
- **Device Data**: Cache latest telemetry for 1 hour
- **User Sessions**: Cache for 24 hours
- **Configuration**: Cache system config for 1 day
- **Cache Keys**: Use consistent naming pattern

### Database Optimization
- **Connection Pooling**: Use appropriate pool sizes
- **Query Optimization**: Avoid N+1 queries
- **Indexing**: Index frequently queried columns
- **Pagination**: Implement pagination for large datasets

## Monitoring and Logging

### Logging Standards
```typescript
Logger.info('Device telemetry processed', {
  deviceId,
  timestamp: new Date().toISOString(),
  dataPoints: telemetry.length
});

Logger.error('Failed to process device command', {
  deviceId,
  command,
  error: error.message
});
```

### Metrics to Track
- API response times
- Database query performance
- MQTT message throughput
- Device online/offline status
- Error rates by endpoint

## Deployment Guidelines

### Environment Configuration
- Use environment variables for all configuration
- Separate configs for dev/staging/production
- Secure sensitive data (passwords, API keys)
- Document all required environment variables

### Production Considerations
- Use PM2 for process management
- Implement health check endpoints
- Set up log rotation
- Monitor resource usage
- Plan for horizontal scaling

## Documentation Requirements

### Code Documentation
- Use JSDoc comments for all public methods
- Document complex business logic
- Provide usage examples
- Keep documentation up to date

### API Documentation
- Generate Swagger documentation
- Document all endpoints
- Provide request/response examples
- Include error response formats

## Common Patterns and Anti-Patterns

### ✅ Good Practices
- Use dependency injection
- Implement proper error handling
- Cache frequently accessed data
- Use transactions for data consistency
- Validate all inputs

### ❌ Anti-Patterns
- Direct database access in controllers
- Hardcoded configuration values
- Missing error handling
- Circular dependencies
- Exposing sensitive data in logs

## Getting Started Checklist

For new developers joining the project:

1. [ ] Set up development environment
2. [ ] Review this guidelines document
3. [ ] Understand the IoT architecture
4. [ ] Set up local databases (PostgreSQL, Redis)
5. [ ] Configure MQTT broker connection
6. [ ] Run tests to verify setup
7. [ ] Review existing code patterns
8. [ ] Start with small feature implementation

## Resources

- [TypeORM Documentation](https://typeorm.io/)
- [NestJS Documentation](https://nestjs.com/)
- [Thingsboard Documentation](https://thingsboard.io/docs/)
- [MQTT Protocol Specification](https://mqtt.org/)
- [Jest Testing Framework](https://jestjs.io/)
