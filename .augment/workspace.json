{"workspace": {"name": "VIIS IoT Backend TypeScript", "type": "iot-backend", "version": "1.0.0", "description": "IoT backend system for agricultural applications with multi-tenant support", "technologies": ["typescript", "nodejs", "express", "<PERSON><PERSON><PERSON>", "postgresql", "redis", "mqtt", "kafka", "thingsboard"]}, "structure": {"src": {"description": "Main source code directory", "subdirectories": {"modules": "Feature modules organized by domain", "services": "External service integrations", "orm": "Database entities and migrations", "interfaces": "TypeScript interface definitions", "utils": "Utility functions and helpers", "loaders": "Application bootstrapping and initialization", "middlewares": "Express middleware functions", "microservices": "NestJS microservice components"}}, "tests": {"description": "Test files and test utilities", "framework": "jest"}, "docs": {"description": "Generated documentation", "generator": "typedoc"}, "tools": {"description": "Development and utility scripts"}}, "conventions": {"fileNaming": {"controllers": "{ModuleName}Controller.ts", "services": "{ModuleName}Service.ts", "interfaces": "I{EntityName}.ts", "entities": "{EntityName}.ts", "dtos": "{Purpose}Dto.ts", "tests": "{ModuleName}.test.ts"}, "directoryStructure": {"modules": "src/modules/{module-name}/", "services": "src/services/{service-name}/", "interfaces": "src/interfaces/", "entities": "src/orm/entities/{category}/", "migrations": "src/orm/migrations/"}}, "iotSpecific": {"deviceManagement": {"location": "src/modules/device/", "thingsboardIntegration": "src/services/thingsboard/", "mqttHandling": "src/loaders/mqttws/"}, "telemetryProcessing": {"caching": "Redis for latest device data", "storage": "PostgreSQL for historical data", "realtime": "MQTT for live updates"}, "multiTenant": {"tenantManagement": "src/modules/tenant/", "customerManagement": "src/modules/iot_customer/", "isolation": "Database level tenant isolation"}}, "integrations": {"databases": {"postgresql": {"erpDatabase": "Business logic and ERP integration", "thingsboardDatabase": "IoT device data and telemetry", "ormDatabase": "Application entities and relationships"}, "redis": {"caching": "Device data and session caching", "queuing": "Background job processing"}}, "messageBrokers": {"mqtt": {"broker": "EMQX", "topics": "viis/things/v2/{deviceId}/{action}"}, "kafka": {"purpose": "Event streaming and microservice communication"}}, "externalServices": {"thingsboard": "IoT platform for device management", "aws": "SNS notifications and S3 storage", "openai": "AI agent functionality", "weatherApi": "Weather data integration"}}, "development": {"scripts": {"dev": "Development server with hot reload", "build": "TypeScript compilation and alias resolution", "test": "Jest test runner", "migration": "TypeORM database migrations", "docs": "TypeDoc documentation generation"}, "environment": {"required": ["Database connection variables", "JWT secrets", "External service endpoints", "MQTT broker configuration"]}}, "deployment": {"production": {"processManager": "PM2", "containerization": "Docker for Redis", "monitoring": "Winston logging"}}, "quality": {"codeStyle": "Prettier + ESLint", "testing": "Jest with coverage reporting", "documentation": "TypeDoc + JSDoc comments", "typeChecking": "Strict TypeScript configuration"}}