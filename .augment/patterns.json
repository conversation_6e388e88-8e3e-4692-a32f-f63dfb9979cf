{"designPatterns": {"controllerPattern": {"description": "REST API controllers using routing-controllers", "template": {"imports": ["import { JsonController, Get, Post, Put, Delete, Param, Body, CurrentUser, Authorized } from 'routing-controllers';", "import { Container } from 'typedi';", "import { ICurrentUser } from '@app/interfaces';"], "structure": {"decorator": "@JsonController('/endpoint')", "authorization": "@Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])", "methods": "CRUD operations with proper HTTP verbs", "errorHandling": "Standardized error responses"}}, "examples": ["src/modules/device/DeviceController.ts", "src/modules/thingsboard/ThingsboardAPIController.ts"]}, "servicePattern": {"description": "Business logic services with dependency injection", "template": {"imports": ["import { Service } from 'typedi';", "import { AppDataSource } from '@app/orm/dataSource';", "import Logger from '@app/loaders/logger';"], "structure": {"decorator": "@Service()", "repository": "AppDataSource.getRepository(Entity)", "errorHandling": "Try-catch with logging", "caching": "Redis integration where appropriate"}}}, "repositoryPattern": {"description": "Data access layer using TypeORM repositories", "template": {"entityDefinition": "TypeORM entities with decorators", "relationships": "Proper foreign key relationships", "migrations": "Database schema versioning"}}, "iotDevicePattern": {"description": "IoT device management and telemetry processing", "components": {"deviceService": "Device CRUD operations and status management", "telemetryProcessor": "Real-time data processing and caching", "commandHandler": "Device control and RPC commands", "eventEmitter": "Device state change notifications"}, "dataFlow": ["Device → MQTT → Thingsboard → Kafka → Backend Processing", "Backend → MQTT → Device (for commands)", "Telemetry → Redis Cache → PostgreSQL Storage"]}, "multiTenantPattern": {"description": "Multi-tenant architecture with data isolation", "isolation": {"database": "Tenant-specific schemas or row-level security", "caching": "Tenant-prefixed cache keys", "authentication": "Tenant-aware JWT tokens"}, "components": {"tenantResolver": "Extract tenant from request context", "dataFilter": "Automatic tenant filtering in queries", "resourceIsolation": "Tenant-specific resource access"}}}, "iotSpecificPatterns": {"telemetryProcessing": {"description": "Real-time IoT data processing pipeline", "stages": [{"name": "ingestion", "description": "Receive telemetry from MQTT/Thingsboard", "location": "src/microservices/device/"}, {"name": "validation", "description": "Validate and normalize sensor data", "location": "src/services/validation/"}, {"name": "caching", "description": "Store latest values in Redis", "location": "src/services/redis/"}, {"name": "persistence", "description": "Store historical data in PostgreSQL", "location": "src/orm/entities/"}, {"name": "alerting", "description": "Process alarms and notifications", "location": "src/modules/alarm/"}]}, "deviceCommandPattern": {"description": "Sending commands to IoT devices", "flow": ["API Request → Validation → Thingsboard RPC → MQTT → Device", "Device Response → MQTT → Thingsboard → Kafka → Backend"], "components": {"commandValidator": "Validate command parameters", "deviceProxy": "Abstract device communication", "responseHandler": "Process device responses"}}, "ruleEnginePattern": {"description": "Business rule processing for IoT events", "implementation": {"thingsboardRules": "Device-level rule chains", "backendRules": "Business logic rule processing", "intentProcessing": "Automated device actions"}, "location": "src/services/thingsboard/rules/"}}, "securityPatterns": {"authentication": {"jwt": {"description": "JWT-based authentication with role-based access", "implementation": "src/modules/auth/", "middleware": "src/middlewares/auth/"}, "basicAuth": {"description": "Basic authentication for system integrations", "usage": "Frappe ERP and Thingsboard integration"}}, "authorization": {"roleBasedAccess": {"userTypes": ["SYSTEM_USER", "VIIS_IOT_USER", "CUSTOMER_USER"], "implementation": "@Authorized decorator on controllers"}, "resourceAccess": {"tenantIsolation": "Users can only access their tenant's resources", "deviceAccess": "Users can only control authorized devices"}}}, "dataPatterns": {"caching": {"deviceData": {"pattern": "viis:device:{deviceId}:latest", "ttl": "1 hour", "purpose": "Latest telemetry values"}, "userSessions": {"pattern": "viis:session:{userId}", "ttl": "24 hours", "purpose": "User authentication state"}}, "databaseNaming": {"tables": "snake_case with iot_ prefix", "columns": "snake_case", "indexes": "idx_{table}_{column}", "foreignKeys": "fk_{table}_{referenced_table}"}}, "integrationPatterns": {"externalServices": {"httpClients": {"description": "Standardized HTTP client with retry and timeout", "implementation": "src/services/AxiosService.ts", "features": ["Request/response logging", "Error handling", "Timeout configuration"]}, "messageQueues": {"kafka": {"producer": "src/services/kafka/kafkaProducer.ts", "consumer": "Event-driven microservices", "topics": "Structured topic naming"}, "mqtt": {"client": "src/loaders/mqttws/MQTTNoti.ts", "topics": "viis/things/v2/{deviceId}/{action}", "qos": "At least once delivery"}}}}, "testingPatterns": {"unitTests": {"location": "tests/", "naming": "{module}.test.ts", "structure": "Arrange-Act-Assert pattern"}, "integrationTests": {"database": "Test database with migrations", "external": "Mock external service calls", "e2e": "Full API endpoint testing"}}}