CREATE OR REPLACE FUNCTION update_overtime_approval_approver_id()
RETURNS TRIGGER AS $$
BEGIN
    -- Chỉ tiếp tục nếu approver_id đã thay đổi
    IF NEW.approver_id IS DISTINCT FROM OLD.approver_id THEN
        -- C<PERSON><PERSON> nhật tất cả các record trong bảng tabiot_workshift_overtime_approval
        -- với iot_user_in_workshift liên kết với iot_workshift vừa được cập nhật
        UPDATE tabiot_workshift_overtime_approval
        SET approver_id = NEW.approver_id
        WHERE iot_user_in_workshift IN (
            SELECT name
            FROM tabiot_user_in_workshift
            WHERE iot_workshift = NEW.name
        );
    END IF;

    -- Trả lại giá trị NEW để hoàn tất UPDATE ban đầu
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


CREATE TRIGGER trg_update_approver_id
AFTER UPDATE ON tabiot_workshift
FOR EACH ROW
WHEN (OLD.approver_id IS DISTINCT FROM NEW.approver_id)
EXECUTE FUNCTION update_overtime_approval_approver_id();