create function activate_single_plan() returns trigger
    language plpgsql
as
$$
BEGIN
    IF NEW.enable = 1 THEN
        -- Disable all other plans for the same customer and device
        UPDATE tabiot_schedule_plan
        SET enable = 0
        WHERE name != NEW.name
          AND customer_id = NEW.customer_id
          AND device_id = NEW.device_id;
    END IF;

--     IF NEW.enable = 0 THEN
--         -- Disable all schedules associated with this plan
--         UPDATE tabiot_schedule
--         SET enable = 0
--         WHERE schedule_plan_id = NEW.name;
--     END IF;

    RETURN NEW;
END;
$$;

alter function activate_single_plan() owner to postgres;

