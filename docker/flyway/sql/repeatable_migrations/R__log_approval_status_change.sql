CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE OR REPLACE FUNCTION log_approval_status_change() 
RETURNS TRIGGER AS $$
DECLARE
    old_status VARCHAR(50);
    new_status VARCHAR(50);
BEGIN
    -- L<PERSON>y trạng thái cũ và trạng thái mới
    old_status := OLD.approval_status;
    new_status := NEW.approval_status;

    -- Chỉ ghi lại lịch sử nếu trạng thái thay đổi
    IF old_status IS DISTINCT FROM new_status THEN
        INSERT INTO tabapproval_status_history (
            "name",
            approval_id, 
            old_status, 
            new_status, 
            changed_by_id, 
            changed_by_role, 
            change_reason, 
            creation,
            modified
        ) VALUES (
            uuid_generate_v4()::text,  -- Tạo giá trị hash ngẫu nhiên cho cột "name"
            OLD.name,                 -- ID của yêu cầu phê duyệt
            old_status,               -- Trạng thái cũ
            new_status,               -- Trạng thái mới
            NEW.modified_by_user,     -- ID người thay đổi
            CASE                      -- <PERSON><PERSON><PERSON> đ<PERSON>nh vai trò của người thay đổi
                WHEN NEW.modified_by_user = OLD.approver_id THEN 'approver'
                ELSE 'creator'
            END, 
            NEW.reason_for_change,    -- Lý do thay đổi
            NOW(),                    -- Thời gian tạo
            NOW()                     -- Thời gian sửa đổi (modified)
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;



CREATE TRIGGER trigger_log_approval_status_change
AFTER UPDATE OF approval_status
ON tabiot_workshift_overtime_approval
FOR EACH ROW
EXECUTE FUNCTION log_approval_status_change();
