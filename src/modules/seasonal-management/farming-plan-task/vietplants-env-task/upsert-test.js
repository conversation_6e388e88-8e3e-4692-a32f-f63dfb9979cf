/**
 * Simple test script to verify upsert functionality
 * Run this script to test the upsert API endpoints
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000'; // Adjust as needed
const API_ENDPOINT = '/seasonal-management/farming-plan-task/env/';

// Test data
const testTaskData = {
  name: 'upsert-test-task-001',
  farming_plan_state: 'test-state-123',
  task_type: 'ENV_STOCK',
  label: 'Upsert Test Task',
  description: 'Testing upsert functionality',
  item_list: [
    {
      name: 'test-item-001',
      quantity: 10,
      description: 'Initial test item',
      iot_category_id: 'test-category-123',
      active_uom: 'kg',
      active_conversion_factor: 1,
      draft_quantity: 5
    }
  ],
  prod_quantity_list: [
    {
      name: 'test-prod-001',
      quantity: 20,
      description: 'Initial test product',
      product_id: 'test-product-123',
      active_uom: 'kg',
      active_conversion_factor: 1,
      draft_quantity: 10
    }
  ]
};

const updatedTaskData = {
  ...testTaskData,
  label: 'Updated Upsert Test Task',
  description: 'Updated testing upsert functionality',
  item_list: [
    {
      name: 'test-item-001',
      quantity: 15, // Updated quantity
      description: 'Updated test item',
      iot_category_id: 'test-category-123',
      active_uom: 'kg',
      active_conversion_factor: 1,
      draft_quantity: 8
    },
    {
      name: 'test-item-002',
      quantity: 25, // New item
      description: 'New test item',
      iot_category_id: 'test-category-456',
      active_uom: 'kg',
      active_conversion_factor: 1,
      draft_quantity: 12
    }
  ],
  prod_quantity_list: [
    {
      name: 'test-prod-001',
      quantity: 30, // Updated quantity
      description: 'Updated test product',
      product_id: 'test-product-123',
      active_uom: 'kg',
      active_conversion_factor: 1,
      draft_quantity: 15
    }
  ]
};

const deleteItemsData = {
  ...testTaskData,
  label: 'Final Upsert Test Task',
  description: 'Testing item deletion',
  item_list: [
    {
      name: 'test-item-002',
      quantity: 30,
      description: 'Only remaining item',
      iot_category_id: 'test-category-456',
      active_uom: 'kg',
      active_conversion_factor: 1,
      draft_quantity: 15
    }
  ],
  prod_quantity_list: [] // Delete all production quantities
};

async function makeRequest(data, description) {
  try {
    console.log(`\n=== ${description} ===`);
    console.log('Request data:', JSON.stringify(data, null, 2));

    const response = await axios.post(`${BASE_URL}${API_ENDPOINT}`, data, {
      headers: {
        'Content-Type': 'application/json',
        // Add authentication headers as needed
        // 'Authorization': 'Bearer your-token-here'
      }
    });

    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));

    // Verify enhanced response format
    verifyResponseFormat(response.data, data);

    return response.data;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
    throw error;
  }
}

function verifyResponseFormat(responseData, requestData) {
  console.log('\n--- Response Format Verification ---');

  // Check if response includes direct field arrays
  const hasItemList = Array.isArray(responseData.item_list);
  const hasProdQuantityList = Array.isArray(responseData.prod_quantity_list);
  const hasTaskTransfers = responseData.task_type === 'ENV_POUR' ? Array.isArray(responseData.task_transfers) : true;

  console.log('✓ item_list array present:', hasItemList);
  console.log('✓ prod_quantity_list array present:', hasProdQuantityList);
  if (responseData.task_type === 'ENV_POUR') {
    console.log('✓ task_transfers array present:', hasTaskTransfers);
  }

  // Check backward compatibility objects
  const hasWarehouseItemsInfo = responseData.warehouseItemsInfo && Array.isArray(responseData.warehouseItemsInfo.items);
  const hasProductionInfo = responseData.productionInfo && Array.isArray(responseData.productionInfo.items);

  console.log('✓ warehouseItemsInfo (backward compatibility):', hasWarehouseItemsInfo);
  console.log('✓ productionInfo (backward compatibility):', hasProductionInfo);

  // Verify field mapping for items
  if (requestData.item_list && responseData.item_list) {
    console.log('✓ Item field mapping verification:');
    requestData.item_list.forEach((reqItem, index) => {
      const respItem = responseData.item_list[index];
      if (respItem) {
        console.log(`  - Item ${reqItem.name}: name ✓, quantity ✓, description ✓`);
        console.log(`    Additional fields: exp_quantity, loss_quantity, task_id, owner, creation, modified`);
      }
    });
  }

  // Verify field mapping for production quantities
  if (requestData.prod_quantity_list && responseData.prod_quantity_list) {
    console.log('✓ Production quantity field mapping verification:');
    requestData.prod_quantity_list.forEach((reqProd, index) => {
      const respProd = responseData.prod_quantity_list[index];
      if (respProd) {
        console.log(`  - Product ${reqProd.name}: name ✓, quantity ✓, product_id ✓`);
        console.log(`    Additional fields: exp_quantity, finished_quantity, task_id, owner, creation, modified`);
      }
    });
  }

  console.log('--- End Verification ---\n');
}

async function runUpsertTests() {
  try {
    console.log('Starting upsert functionality tests...');

    // Test 1: Create new task
    await makeRequest(testTaskData, 'Test 1: Create New Task');

    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test 2: Update existing task (upsert)
    await makeRequest(updatedTaskData, 'Test 2: Update Existing Task (Upsert)');

    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test 3: Delete items by omitting from array
    await makeRequest(deleteItemsData, 'Test 3: Delete Items by Omitting from Array');

    console.log('\n=== All tests completed successfully! ===');

  } catch (error) {
    console.error('\n=== Test failed ===');
    console.error('Error:', error.message);
  }
}

// ENV_POUR task test
const envPourTaskData = {
  name: 'upsert-test-pour-001',
  farming_plan_state: 'test-state-123',
  task_type: 'ENV_POUR',
  label: 'Upsert Test Pour Task',
  description: 'Testing ENV_POUR upsert functionality',
  task_transfers: [
    {
      source_task_id: 'upsert-test-task-001',
      source_task_type: 'ENV_STOCK',
      description: 'Transfer from stock task'
    }
  ],
  item_list: [
    {
      name: 'pour-item-001',
      quantity: 5,
      description: 'Pour item 1',
      iot_category_id: 'test-category-789',
      active_uom: 'kg',
      active_conversion_factor: 1,
      draft_quantity: 2
    }
  ]
};

async function runEnvPourTest() {
  try {
    console.log('\n=== Starting ENV_POUR upsert test ===');
    await makeRequest(envPourTaskData, 'ENV_POUR Task Creation/Update');
    console.log('\n=== ENV_POUR test completed successfully! ===');
  } catch (error) {
    console.error('\n=== ENV_POUR test failed ===');
    console.error('Error:', error.message);
  }
}

// Run tests
async function main() {
  await runUpsertTests();
  await runEnvPourTest();
}

// Uncomment the line below to run the tests
// main();

module.exports = {
  runUpsertTests,
  runEnvPourTest,
  testTaskData,
  updatedTaskData,
  deleteItemsData,
  envPourTaskData
};
