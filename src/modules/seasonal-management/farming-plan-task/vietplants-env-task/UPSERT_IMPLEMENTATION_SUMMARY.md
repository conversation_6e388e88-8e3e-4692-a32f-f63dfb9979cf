# Upsert Functionality Implementation Summary

## Overview

Successfully implemented comprehensive upsert functionality for the seasonal-management farming plan task CREATE API. The implementation supports upserting the main task and all related tables with proper transaction handling and error management.

## Key Features Implemented

### 1. Main Task Upsert Logic
- **File Modified:** `VietplantsEnvFarmingPlanTaskService.ts`
- **Method:** `createTask()`
- **Functionality:**
  - Checks if task with given name exists
  - Updates existing task or creates new one
  - Maintains proper audit fields (`modified`, `modified_by`, `creation`, `created_by`)
  - Supports all task types (ENV_STOCK, ENV_STEAM_POT, ENV_POUR)

### 2. Warehouse Items Upsert
- **Method:** `upsertWarehouseItems()`
- **Logic:**
  - Items with `name` field: Update if exists, create if doesn't exist
  - Items without `name` field: Create as new records with generated UUID
  - Missing items: Delete from database if not present in incoming array
  - Maintains referential integrity with task

### 3. Production Quantities Upsert
- **Method:** `upsertProductionQuantities()`
- **Logic:**
  - Same upsert pattern as warehouse items
  - Updates existing quantities by name
  - Creates new quantities for items without names
  - Deletes quantities not present in incoming array

### 4. Task Transfers Upsert (ENV_POUR)
- **Method:** `upsertTaskTransfers()`
- **Logic:**
  - Updates existing transfers based on source_task_id
  - Creates new transfers for new source tasks
  - Deletes transfers not present in incoming array
  - Maintains task linking for ENV_POUR tasks

### 5. Enhanced ENV_POUR Support
- **Method:** `createEnvPourTask()`
- **Features:**
  - Supports upsert for main task, items, quantities, and transfers
  - Proper transaction handling for complex operations
  - Validation of source task types (ENV_STOCK, ENV_STEAM_POT only)

## Implementation Details

### Transaction Management
- All operations wrapped in database transactions
- Automatic rollback on errors
- Ensures data consistency across all related tables

### Error Handling
- Comprehensive error logging
- Proper error propagation
- Transaction rollback on failures
- Maintains data integrity

### Enhanced Response Format
- **Direct field arrays**: Response includes `item_list`, `prod_quantity_list`, and `task_transfers` in request body structure
- **Complete field mapping**: All request fields plus additional database fields included
- **Better debugging**: Easy comparison between request and response data
- **Backward compatibility**: Existing computed info objects maintained (`warehouseItemsInfo`, `productionInfo`, `taskTransferInfo`)

### Backward Compatibility
- Existing API response format maintained alongside new format
- No breaking changes to existing functionality
- Supports both create and update operations seamlessly

## API Usage Examples

### Basic Task Upsert
```json
POST /seasonal-management/farming-plan-task/env/
{
  "name": "task-001",
  "farming_plan_state": "state-123",
  "task_type": "ENV_STOCK",
  "label": "Updated Task",
  "item_list": [
    {
      "name": "item-001",
      "quantity": 15,
      "iot_category_id": "category-123"
    }
  ]
}
```

### ENV_POUR with Transfers
```json
POST /seasonal-management/farming-plan-task/env/
{
  "name": "pour-task-001",
  "task_type": "ENV_POUR",
  "task_transfers": [
    {
      "source_task_id": "stock-task-001",
      "source_task_type": "ENV_STOCK"
    }
  ],
  "item_list": [...]
}
```

### Delete Items by Omission
```json
POST /seasonal-management/farming-plan-task/env/
{
  "name": "task-001",
  "item_list": [],  // Deletes all existing items
  "prod_quantity_list": [
    // Only items listed here will remain
  ]
}
```

### Enhanced Response Format Example
```json
{
  "name": "task-001",
  "farming_plan_state": "state-123",
  "task_type": "ENV_STOCK",
  "label": "Task Label",

  // Direct field arrays (NEW - matches request structure)
  "item_list": [
    {
      "name": "item-001",
      "quantity": 15,
      "description": "Item description",
      "iot_category_id": "category-123",
      "active_uom": "kg",
      "active_conversion_factor": 1,
      "draft_quantity": 5,
      // Additional database fields
      "exp_quantity": 0,
      "loss_quantity": 0,
      "task_id": "task-001",
      "owner": "customer-id",
      "creation": "2024-06-20T10:00:00Z",
      "modified": "2024-06-20T10:00:00Z",
      "iotCategory": { "name": "category-123", "label": "Category Label" }
    }
  ],
  "prod_quantity_list": [...],

  // Backward compatibility objects (maintained)
  "warehouseItemsInfo": { "items": [...], "total_items": 1 },
  "productionInfo": { "items": [...], "total_items": 0 }
}
```

## Testing

### Test Files Created
1. **test-upsert.md** - Comprehensive test scenarios and verification steps
2. **upsert-test.js** - Automated test script for API validation

### Test Scenarios Covered
- Create new tasks with related data
- Update existing tasks (main fields)
- Upsert warehouse items and production quantities
- Delete items by omitting from arrays
- ENV_POUR task creation and updates
- Task transfer management
- Error handling and rollback scenarios

## Database Impact

### Tables Modified
- `tabiot_farming_plan_task` (main task table)
- `tabiot_warehouse_item_task_used` (warehouse items)
- `tabiot_production_quantity` (production quantities)
- `tabiot_task_item_transfer` (task transfers)

### Performance Considerations
- Efficient queries using existing indexes
- Batch operations for related entities
- Minimal database round trips
- Proper use of TypeORM query builders

## Security & Validation

### Input Validation
- Existing DTO validation maintained
- Task type validation for transfers
- Source task existence validation
- Foreign key constraint handling

### Audit Trail
- Proper audit field updates
- User tracking for modifications
- Timestamp management
- Ownership preservation

## Deployment Notes

### Prerequisites
- No database migrations required
- Existing entity relationships sufficient
- TypeORM configuration unchanged

### Rollback Plan
- Implementation is backward compatible
- Can revert to previous version without data loss
- No breaking changes to existing APIs

## Future Enhancements

### Potential Improvements
1. **Bulk Operations:** Support for bulk upsert of multiple tasks
2. **Partial Updates:** Support for partial field updates
3. **Conflict Resolution:** Advanced conflict resolution strategies
4. **Performance Optimization:** Query optimization for large datasets
5. **Audit Logging:** Enhanced audit trail with change tracking

### Monitoring Recommendations
- Monitor transaction duration
- Track upsert vs create ratios
- Monitor error rates and rollback frequency
- Performance metrics for large operations

## Conclusion

The upsert functionality has been successfully implemented with comprehensive support for all task types and related entities. The implementation maintains data integrity, provides proper error handling, and ensures backward compatibility while adding powerful new capabilities for task management.
