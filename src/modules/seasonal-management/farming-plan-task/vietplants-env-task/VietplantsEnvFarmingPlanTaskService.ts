import Container, { Service } from 'typedi';
import { HttpError } from 'routing-controllers';
import Logger from '@app/loaders/logger';
import { ICurrentUser } from '@app/interfaces';
import { CreateFarmingPlanTaskDto } from '../task/FarmingPlanTask.dto';
import { BulkCreateTaskDto, BulkDeleteTaskDto, TaskTransferDto, WarehouseItemTaskUsedDto, ProductionQuantityDto, ApproveEnvPourTaskDto } from './VietplantsEnvFarmingPlanTask.dto';
import { FilterTuple } from '@app/utils/helpers/queryHelpter';
import { FarmingPlanTask } from '@app/orm/entities/farmingPlan/FarmingPlanTask';
import { TaskService } from '@app/modules/farming-plan/task/TaskService';
import { AppDataSource } from '@app/orm/dataSource';
import { applyQueryFilters } from '@app/utils/helpers/queryHelpter';
import { isVietPlantsTenant } from '@app/utils/vietplants/vietplants-utils';
import { TaskItemTransfer } from '@app/orm/entities/farmingPlan/taskItem/TaskItemTransfer';
import { WarehouseItemTaskUsed } from '@app/orm/entities/farmingPlan/taskItem/WarehouseItemTaskUsed';
import { ProductionQuantity } from '@app/orm/entities/farmingPlan/taskItem/ProductionQuantity';
import { ProductionPlanAllocationService } from '../../production-plan/ProductionPlanAllocationService';
import { ProductionPlanYieldExpectedOutput, ProductionPlan } from '@app/orm/entities/productionPlan/ProductionPlan';
import { TaskItemTransferService } from '../vietplants-item-transfer/TaskItemTransferService';
import { LabelPrintManagementService } from '../vietplants-label-print-management/LabelPrintManagementService';
import { IotCustomerUser } from '@app/orm/entities/customer/customer_user';
import { v4 as uuidv4 } from 'uuid';

@Service()
export class FarmingPlanTaskService {
  private oldTaskService: TaskService;
  private taskItemTransferService: TaskItemTransferService;
  private labelPrintManagementService: LabelPrintManagementService;
  private allocationService: ProductionPlanAllocationService;
  private yieldExpectedOutputRepository = AppDataSource.getRepository(ProductionPlanYieldExpectedOutput);
  private productionPlanRepository = AppDataSource.getRepository(ProductionPlan);
  private taskRepo = AppDataSource.getRepository(FarmingPlanTask);
  constructor() {
    this.oldTaskService = Container.get(TaskService);
    this.taskItemTransferService = Container.get(TaskItemTransferService);
    this.labelPrintManagementService = Container.get(LabelPrintManagementService);
    this.allocationService = Container.get(ProductionPlanAllocationService);
  }

  /**
   * Gets a task by its ID with enhanced response format
   * Includes related arrays in request body structure for better debugging and field mapping
   * @param user Current user
   * @param taskId Task ID
   * @returns Enhanced FarmingPlanTask with related arrays
   */
  async getTaskById(user: ICurrentUser, taskId: string): Promise<any> {
    try {
      const task = await this.taskRepo.findOne({
        where: { name: taskId },
        relations: ['farmingPlanState', 'assignedUser', 'department', 'environmentTemplate', 'template']
      });

      // Verify the task belongs to the current customer
      if (task && user.customer?.id && task.owner !== user.customer.id.toString()) {
        throw new HttpError(403, `Unauthorized access to task ${taskId}`);
      }

      if (!task) {
        throw new HttpError(404, `Task ${taskId} not found`);
      }

      // Load related data for enhanced response
      const [warehouseItems, productionQuantities, taskTransfers] = await Promise.all([
        this.getWarehouseItemsForTask(taskId),
        this.getProductionQuantitiesForTask(taskId),
        this.getTaskTransfersForTask(taskId)
      ]);

      // Build enhanced response with request body structure
      const enhancedTask = {
        ...task,
        // Direct field arrays matching request structure
        item_list: warehouseItems,
        prod_quantity_list: productionQuantities,
        ...((task.task_type as string) === 'ENV_POUR' && { task_transfers: taskTransfers }),

        // Backward compatibility - computed info objects
        warehouseItemsInfo: {
          items: warehouseItems.map((item: any) => ({
            name: item.name,
            iot_category_id: item.iot_category_id,
            category_name: item.iotCategory?.name,
            category_label: item.iotCategory?.label,
            quantity: item.quantity,
            exp_quantity: item.exp_quantity,
            loss_quantity: item.loss_quantity,
            issued_quantity: item.issued_quantity,
            draft_quantity: item.draft_quantity,
            total_qty_in_crop: item.total_qty_in_crop,
            active_uom: item.active_uom,
            uom_name: item.activeUOM?.name,
            active_conversion_factor: item.active_conversion_factor,
            description: item.description
          })),
          total_items: warehouseItems.length
        },
        productionInfo: {
          items: productionQuantities.map((pq: any) => ({
            name: pq.name,
            product_id: pq.product_id,
            product_name: pq.product?.name,
            product_label: pq.product?.label,
            quantity: pq.quantity,
            exp_quantity: pq.exp_quantity,
            finished_quantity: pq.finished_quantity,
            lost_quantity: pq.lost_quantity,
            draft_quantity: pq.draft_quantity,
            issued_quantity: pq.issued_quantity,
            total_qty_in_crop: pq.total_qty_in_crop,
            active_uom: pq.active_uom,
            uom_name: pq.activeUOM?.name,
            active_conversion_factor: pq.active_conversion_factor,
            description: pq.description
          })),
          total_items: productionQuantities.length
        },
        ...((task.task_type as string) === 'ENV_POUR' && {
          taskTransferInfo: {
            transfers: taskTransfers.map((transfer: any) => ({
              name: transfer.name,
              source_task_id: transfer.source_task_id,
              target_task_id: transfer.target_task_id,
              source_task_type: transfer.sourceTask?.task_type,
              description: transfer.description,
              transfer_date: transfer.transfer_date,
              status: transfer.status
            })),
            total_transfers: taskTransfers.length
          }
        })
      };

      return enhancedTask;
    } catch (error) {
      Logger.error(`Error getting task by ID:`, error);
      throw error;
    }
  }

  /**
   * Get warehouse items for a task in request body format
   */
  private async getWarehouseItemsForTask(taskId: string): Promise<any[]> {
    const warehouseItems = await AppDataSource.getRepository(WarehouseItemTaskUsed)
      .createQueryBuilder('item')
      .leftJoinAndSelect('item.iotCategory', 'iotCategory')
      .leftJoinAndSelect('item.activeUOM', 'activeUOM')
      .where('item.task_id = :taskId', { taskId })
      .andWhere('item.deleted IS NULL')
      .getMany();

    return warehouseItems.map((item: WarehouseItemTaskUsed) => ({
      name: item.name,
      quantity: item.quantity,
      description: item.description,
      iot_category_id: item.iot_category_id,
      active_uom: item.active_uom,
      active_conversion_factor: item.active_conversion_factor,
      draft_quantity: item.draft_quantity,
      // Additional database fields
      exp_quantity: item.exp_quantity,
      loss_quantity: item.loss_quantity,
      issued_quantity: item.issued_quantity,
      total_qty_in_crop: item.total_qty_in_crop,
      task_id: item.task_id,
      owner: item.owner,
      creation: item.creation,
      modified: item.modified,
      modified_by: item.modified_by,
      // Related entity data
      iotCategory: item.iotCategory,
      activeUOM: item.activeUOM
    }));
  }

  /**
   * Get production quantities for a task in request body format
   */
  private async getProductionQuantitiesForTask(taskId: string): Promise<any[]> {
    const productionQuantities = await AppDataSource.getRepository(ProductionQuantity)
      .createQueryBuilder('prod')
      .leftJoinAndSelect('prod.product', 'product')
      .leftJoinAndSelect('prod.activeUOM', 'activeUOM')
      .where('prod.task_id = :taskId', { taskId })
      .andWhere('prod.deleted IS NULL')
      .getMany();

    return productionQuantities.map((prod: ProductionQuantity) => ({
      name: prod.name,
      quantity: prod.quantity,
      description: prod.description,
      product_id: prod.product_id,
      active_uom: prod.active_uom,
      active_conversion_factor: prod.active_conversion_factor,
      draft_quantity: prod.draft_quantity,
      // Additional database fields
      exp_quantity: prod.exp_quantity,
      lost_quantity: prod.lost_quantity,
      finished_quantity: prod.finished_quantity,
      issued_quantity: prod.issued_quantity,
      total_qty_in_crop: prod.total_qty_in_crop,
      task_id: prod.task_id,
      owner: prod.owner,
      creation: prod.creation,
      modified: prod.modified,
      modified_by: prod.modified_by,
      // Related entity data
      product: prod.product,
      activeUOM: prod.activeUOM
    }));
  }

  /**
   * Get task transfers for a task in request body format
   */
  private async getTaskTransfersForTask(taskId: string): Promise<any[]> {
    const taskTransfers = await AppDataSource.getRepository(TaskItemTransfer)
      .createQueryBuilder('transfer')
      .leftJoinAndSelect('transfer.sourceTask', 'sourceTask')
      .leftJoinAndSelect('transfer.targetTask', 'targetTask')
      .where('transfer.target_task_id = :taskId', { taskId })
      .andWhere('transfer.deleted IS NULL')
      .getMany();

    return taskTransfers.map((transfer: TaskItemTransfer) => ({
      source_task_id: transfer.source_task_id,
      source_task_type: transfer.sourceTask?.task_type,
      description: transfer.description,
      // Additional database fields
      name: transfer.name,
      target_task_id: transfer.target_task_id,
      item_id: transfer.item_id,
      quantity: transfer.quantity,
      conversion_factor: transfer.conversion_factor,
      transfer_date: transfer.transfer_date,
      transfer_by: transfer.transfer_by,
      status: transfer.status,
      owner: transfer.owner,
      creation: transfer.creation,
      modified: transfer.modified,
      modified_by: transfer.modified_by,
      // Related entity data
      sourceTask: transfer.sourceTask,
      targetTask: transfer.targetTask
    }));
  }


  /**
   * Validates if a task can perform a specific operation based on its type
   * @param user Current user
   * @param taskId Task ID
   * @param requiredType Required task type for the operation
   * @returns Boolean indicating if the operation is allowed
   */
  /**
   * Get task management info with filtering and pagination
   * This implementation maintains backward compatibility with the old API format
   * while using TypeORM for better performance and maintainability
   */
  async getTaskManagementInfo(
    user: ICurrentUser,
    page: number = 1,
    size: number = 10,
    filters?: FilterTuple[],
    stateId?: string,
    templateId?: string,
    status?: string,
    assignedTo?: string,
    taskType?: string,
    orderBy?: string,
    productionPlanId?: string
  ) {
    try {
      const skip = (page - 1) * size;
      const take = size;

      // Create query builder with all necessary joins but select only required fields
      let qb = AppDataSource.getRepository(FarmingPlanTask)
        .createQueryBuilder('task')
        .leftJoin('task.farmingPlanState', 'state')
        .leftJoin('state.farmingPlan', 'farmingPlan')
        .leftJoin('farmingPlan.cropRelation', 'crop')
        .leftJoin('crop.zoneRelation', 'zone')
        .leftJoin('task.template', 'template')
        .leftJoin('task.assignedUser', 'assignedUser')
        .leftJoin('task.department', 'department')
        .leftJoin('task.environmentTemplate', 'environmentTemplate');

      // Select task fields
      qb = qb.select('task');

      // Select only required fields from related entities
      qb = qb.addSelect([
        'state.name', 'state.label',
        'farmingPlan.name',
        'crop.name',
        'crop.label',
        'zone.name', 'zone.customer_id',
        'template.name', 'template.label',
        'assignedUser.name', 'assignedUser.email', 'assignedUser.first_name', 'assignedUser.last_name',
        'department.name', 'department.label',
        'environmentTemplate.name', 'environmentTemplate.label', 'environmentTemplate.environment_code'
      ]);

      // Add VietPlants extension if applicable
      if (user.tenant?.name && isVietPlantsTenant(user.tenant.name)) {
        qb = qb.leftJoin('task.vietplantsExtension', 'vietplantsExtension')
          .addSelect('vietplantsExtension');
      }

      // Add task item transfers information with selective fields
      qb = qb.leftJoin('task.incomingTransfers', 'incomingTransfers')
        .leftJoin('incomingTransfers.sourceTask', 'parentTasks')
        .leftJoin('incomingTransfers.item', 'incomingItems')
        .leftJoin('task.outgoingTransfers', 'outgoingTransfers')
        .leftJoin('outgoingTransfers.targetTask', 'childTasks')
        .leftJoin('outgoingTransfers.item', 'outgoingItems')
        .addSelect([
          'incomingTransfers.name', 'incomingTransfers.source_task_id', 'incomingTransfers.item_id',
          'incomingTransfers.quantity', 'incomingTransfers.uom_id',
          'parentTasks.name',
          'incomingItems.name',
          'outgoingTransfers.name', 'outgoingTransfers.target_task_id',
          'childTasks.name',
          'outgoingItems.name'
        ]);

      // Base conditions
      qb = qb.where('task.deleted IS NULL')
        .andWhere('zone.customer_id = :customer_id', { customer_id: user.customer_id });

      // Apply filters
      if (stateId) {
        qb = qb.andWhere('task.farming_plan_state = :stateId', { stateId });
      }

      if (templateId) {
        qb = qb.andWhere('task.template_id = :templateId', { templateId });
      }

      if (status) {
        qb = qb.andWhere('task.status = :status', { status });
      }

      if (assignedTo) {
        qb = qb.andWhere('task.assigned_to = :assignedTo', { assignedTo });
      }

      if (taskType) {
        qb = qb.andWhere('task.task_type = :taskType', { taskType });
      }

      //default filter task_type like ENV
      qb = qb.andWhere('task.task_type LIKE :taskType', { taskType: '%Env%' });

      // Apply dynamic filters
      qb = applyQueryFilters(qb, filters, 'task');

      // Apply production plan filter if specified
      if (productionPlanId) {
        // Join with production plan yield output to filter tasks that have yield output in the specified production plan
        // This filters tasks based on environment template, date overlap, and optionally item_id matching with production quantities
        qb = qb.andWhere(`EXISTS (
          SELECT 1 FROM production_plan_yield_expected_output yieldOutput
          INNER JOIN production_plan productionPlan ON yieldOutput.production_plan_id = productionPlan.name
          WHERE yieldOutput.environment_template_id = task.environment_template_id
            AND yieldOutput.production_plan_id = :productionPlanId
            AND productionPlan.customer_id = :customer_id
            AND yieldOutput.start_date <= task.end_date
            AND yieldOutput.end_date >= task.start_date
            AND (
              yieldOutput.item_id IS NULL
              OR NOT EXISTS (
                SELECT 1 FROM tabiot_production_quantity pq
                WHERE pq.task_id = task.name AND pq.deleted IS NULL
              )
              OR EXISTS (
                SELECT 1 FROM tabiot_production_quantity pq
                WHERE pq.task_id = task.name
                  AND pq.deleted IS NULL
                  AND pq.product_id = yieldOutput.item_id
              )
            )
        )`, { productionPlanId });
        //add debug
        console.log("productionPlanId with item_id matching", productionPlanId);
      }

      // Apply ordering
      if (orderBy) {
        // Handle complex order format like: "iot_farming_plan_task".label DESC
        // or simple format like: field:direction
        if (orderBy.includes(':')) {
          // Simple format: field:direction
          const [field, direction] = orderBy.split(':');
          qb = qb.orderBy(`task.${field}`, direction.toUpperCase() as 'ASC' | 'DESC');
        } else {
          // Complex format: "table".field direction or table.field direction
          const orderParts = orderBy.trim().split(/\s+/);
          if (orderParts.length >= 2) {
            const fieldPart = orderParts[0];
            const direction = orderParts[orderParts.length - 1].toUpperCase() as 'ASC' | 'DESC';

            // Remove quotes if present and extract table and field
            const cleanFieldPart = fieldPart.replace(/"/g, '');

            // Map table names to query builder aliases
            const tableAliasMap: { [key: string]: string } = {
              'iot_farming_plan_task': 'task',
              'iot_farming_plan_state': 'state',
              'iot_farming_plan': 'farmingPlan',
              'iot_environment_template': 'template'
            };

            if (cleanFieldPart.includes('.')) {
              const [tableName, fieldName] = cleanFieldPart.split('.');
              const alias = tableAliasMap[tableName] || tableName;
              qb = qb.orderBy(`${alias}.${fieldName}`, direction);
            } else {
              // No table specified, assume it's a task field
              qb = qb.orderBy(`task.${cleanFieldPart}`, direction);
            }
          } else {
            // Fallback: treat as field name only
            qb = qb.orderBy(`task.${orderBy}`, 'ASC');
          }
        }
      } else {
        // Default ordering
        qb = qb.orderBy('task.creation', 'DESC');
      }

      // Apply pagination
      qb = qb.skip(skip).take(take);

      // Execute query
      const [tasks, total] = await qb.getManyAndCount();

      // Process tasks to include chain information and related production/warehouse items
      const processedTasks = await Promise.all(tasks.map(async (task) => {
        // Get parent and child task counts for the new multi-parent model
        const parentTaskCount = await AppDataSource.getRepository(TaskItemTransfer)
          .createQueryBuilder('transfer')
          .where('transfer.target_task_id = :taskId', { taskId: task.name })
          .getCount();

        const childTaskCount = await AppDataSource.getRepository(TaskItemTransfer)
          .createQueryBuilder('transfer')
          .where('transfer.source_task_id = :taskId', { taskId: task.name })
          .getCount();

        // Get matching yield output and production plan for debug purposes (if productionPlanId filter was used)
        let matchingYieldOutput = null;
        let matchingProductionPlan = null;
        if (productionPlanId && task.environment_template_id && task.start_date && task.end_date) {
          try {
            // Get production quantities for this task to check item_id matching
            const taskProductionQuantities = await AppDataSource.getRepository(ProductionQuantity)
              .createQueryBuilder('prodQty')
              .leftJoinAndSelect('prodQty.product', 'product')
              .where('prodQty.task_id = :taskId', { taskId: task.name })
              .andWhere('prodQty.deleted IS NULL')
              .getMany();

            // Find yield output that matches environment template, date range, and item_id
            let bestMatchingYieldOutput = null;

            // First try to find yield output with matching item_id
            if (taskProductionQuantities.length > 0) {
              for (const prodQty of taskProductionQuantities) {
                // Only proceed if product_id is defined
                if (prodQty.product_id) {
                  const yieldOutput = await this.findMatchingYieldOutputWithItemId(
                    user,
                    task.environment_template_id,
                    task.start_date instanceof Date ? task.start_date.toISOString().split('T')[0] : task.start_date,
                    task.end_date instanceof Date ? task.end_date.toISOString().split('T')[0] : task.end_date,
                    productionPlanId,
                    prodQty.product_id
                  );

                  if (yieldOutput) {
                    bestMatchingYieldOutput = yieldOutput;
                    break; // Use the first matching yield output
                  }
                }
              }
            }

            // If no item-specific match found, fall back to environment template + date matching
            if (!bestMatchingYieldOutput) {
              bestMatchingYieldOutput = await this.findMatchingYieldOutput(
                user,
                task.environment_template_id,
                task.start_date instanceof Date ? task.start_date.toISOString().split('T')[0] : task.start_date,
                task.end_date instanceof Date ? task.end_date.toISOString().split('T')[0] : task.end_date,
                productionPlanId
              );
            }

            if (bestMatchingYieldOutput) {
              matchingYieldOutput = {
                name: bestMatchingYieldOutput.name,
                production_plan_id: bestMatchingYieldOutput.production_plan_id,
                environment_template_id: bestMatchingYieldOutput.environment_template_id,
                item_id: bestMatchingYieldOutput.item_id,
                start_date: bestMatchingYieldOutput.start_date,
                end_date: bestMatchingYieldOutput.end_date,
                yield_value: bestMatchingYieldOutput.yield_value,
                expected_output: bestMatchingYieldOutput.expected_output,
                allocated_quantity: bestMatchingYieldOutput.allocated_quantity,
                remaining_quantity: bestMatchingYieldOutput.remaining_quantity,
                // Add matching info for debug
                matchingCriteria: {
                  environmentTemplateMatch: true,
                  dateOverlap: true,
                  itemIdMatch: bestMatchingYieldOutput.item_id && taskProductionQuantities.some(pq => pq.product_id === bestMatchingYieldOutput.item_id),
                  taskProductIds: taskProductionQuantities.map(pq => pq.product_id)
                }
              };

              // Get production plan details
              const productionPlan = await this.productionPlanRepository.findOne({
                where: { name: bestMatchingYieldOutput.production_plan_id },
                select: ['name', 'label', 'customer_id', 'plant_id', 'start_date', 'end_date', 'status']
              });

              if (productionPlan) {
                matchingProductionPlan = {
                  name: productionPlan.name,
                  label: productionPlan.label,
                  customer_id: productionPlan.customer_id,
                  plant_id: productionPlan.plant_id,
                  start_date: productionPlan.start_date,
                  end_date: productionPlan.end_date,
                  status: productionPlan.status
                };
              }
            }
          } catch (error) {
            Logger.error(`Error getting yield output for task ${task.name}:`, error);
          }
        }

        // Group item transfers by parent task
        const itemsByParent: Record<string, any[]> = {};
        if (task.incomingTransfers && task.incomingTransfers.length > 0) {
          task.incomingTransfers.forEach(transfer => {
            if (!itemsByParent[transfer.source_task_id]) {
              itemsByParent[transfer.source_task_id] = [];
            }
            itemsByParent[transfer.source_task_id].push({
              itemId: transfer.item_id,
              itemName: transfer.item?.name,
              quantity: transfer.quantity,
              uomId: transfer.uom_id
            });
          });
        }

        // Get production quantities related to this task
        const productionQuantities = await AppDataSource.getRepository(ProductionQuantity)
          .createQueryBuilder('prodQty')
          .leftJoinAndSelect('prodQty.product', 'product')
          .leftJoinAndSelect('prodQty.activeUOM', 'activeUOM')
          .where('prodQty.task_id = :taskId', { taskId: task.name })
          .andWhere('prodQty.deleted IS NULL')
          .getMany();

        // Get warehouse items used in this task
        const warehouseItemsUsed = await AppDataSource.getRepository(WarehouseItemTaskUsed)
          .createQueryBuilder('warehouseItem')
          .leftJoinAndSelect('warehouseItem.iotCategory', 'iotCategory')
          .leftJoinAndSelect('warehouseItem.activeUOM', 'activeUOM')
          .where('warehouseItem.task_id = :taskId', { taskId: task.name })
          .andWhere('warehouseItem.deleted IS NULL')
          .getMany();

        // Return enhanced task with chaining information and related items
        return {
          ...task,
          taskChainInfo: {
            parentTaskCount,
            childTaskCount,
            itemsByParent,
          },
          productionInfo: {
            items: productionQuantities.map(pq => ({
              name: pq.name,
              product_id: pq.product_id,
              product_name: pq.product?.name,
              product_label: pq.product?.label,
              quantity: pq.quantity,
              exp_quantity: pq.exp_quantity,
              finished_quantity: pq.finished_quantity,
              lost_quantity: pq.lost_quantity,
              draft_quantity: pq.draft_quantity,
              issued_quantity: pq.issued_quantity,
              total_qty_in_crop: pq.total_qty_in_crop,
              active_uom: pq.active_uom,
              uom_name: pq.activeUOM?.name,
              active_conversion_factor: pq.active_conversion_factor,
              description: pq.description
            })),
            total_items: productionQuantities.length
          },
          warehouseItemsInfo: {
            items: warehouseItemsUsed.map(wi => ({
              name: wi.name,
              iot_category_id: wi.iot_category_id,
              category_name: wi.iotCategory?.name,
              category_label: wi.iotCategory?.label,
              quantity: wi.quantity,
              exp_quantity: wi.exp_quantity,
              loss_quantity: wi.loss_quantity,
              issued_quantity: wi.issued_quantity,
              draft_quantity: wi.draft_quantity,
              total_qty_in_crop: wi.total_qty_in_crop,
              active_uom: wi.active_uom,
              uom_name: wi.activeUOM?.name,
              active_conversion_factor: wi.active_conversion_factor,
              description: wi.description
            })),
            total_items: warehouseItemsUsed.length
          },
          // Add debug information for production plan filter
          debugInfo: {
            productionPlanFilter: productionPlanId ? {
              applied: true,
              productionPlanId: productionPlanId,
              matchingYieldOutput: matchingYieldOutput,
              matchingProductionPlan: matchingProductionPlan
            } : {
              applied: false
            }
          }
        };
      }));

      // Format response to match old API structure for backward compatibility
      const formattedTasks = await this.formatTasksForBackwardCompatibility(processedTasks, user);

      const totalPages = Math.ceil(total / size);
      const pagination = {
        pageNumber: parseInt(page.toString()),
        pageSize: parseInt(size.toString()),
        totalElements: total,
        totalPages: totalPages,
      };

      return {
        data: formattedTasks,
        pagination: pagination,
      };
    } catch (error) {
      Logger.error('Error getting task management info:', error);
      throw error;
    }
  }

  /**
   * Update a task with partial data
   * Any field included in the request body will be updated
   * @param user Current user
   * @param taskId Task ID to update
   * @param updateData Partial data to update
   * @returns Updated task
   */
  async updateTask(user: ICurrentUser, taskId: string, updateData: any): Promise<FarmingPlanTask> {
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the existing task
      const task = await this.getTaskById(user, taskId);

      // Prevent task from linking to itself
      if (updateData.previous_task_id === taskId) {
        throw new HttpError(400, `Cannot link a task to itself`);
      }

      // Extract item_list and prod_quantity_list before updating task
      const { item_list, prod_quantity_list, ...taskDataWithoutLists } = updateData;

      // Store previous task ID before update
      const oldPreviousTaskId = task.previous_task_id;

      // Handle chain linking if previous_task_id has changed
      if (updateData.previous_task_id !== undefined && updateData.previous_task_id !== oldPreviousTaskId) {
        // Double-check to prevent self-reference
        if (updateData.previous_task_id === taskId) {
          throw new HttpError(400, `Cannot link a task to itself`);
        }

        // Create a clean copy of update data without chain-related fields
        const cleanUpdateData = { ...updateData };
        delete cleanUpdateData.previous_task_id;
        delete cleanUpdateData.task_chain_ids;

        // Remove all relation fields and computed properties
        this.cleanUpdateDataObject(cleanUpdateData);

        // Update the basic task data first
        const repo = AppDataSource.getRepository(FarmingPlanTask);

        if (Object.keys(cleanUpdateData).length > 0) {
          await repo.createQueryBuilder()
            .update(FarmingPlanTask)
            .set(cleanUpdateData)
            .where("name = :taskId", { taskId })
            .execute();
        }

        if (updateData.previous_task_id && updateData.previous_task_id.trim() !== '') {
          // Link to a new previous task
          try {
            // Update the previous_task_id directly since updateTaskChain method doesn't exist
            await repo.createQueryBuilder()
              .update(FarmingPlanTask)
              .set({
                previous_task_id: updateData.previous_task_id
              })
              .where("name = :taskId", { taskId })
              .execute();

            // Fetch the updated task with all relations
            return await this.getTaskById(user, taskId);
          } catch (chainError) {
            Logger.error(`Error updating task chain:`, chainError);
            throw chainError; // Re-throw to prevent invalid state
          }
        } else {
          // Remove from chain (previous_task_id set to empty string)
          // Use direct SQL update to avoid any TypeORM relation issues
          await repo.createQueryBuilder()
            .update(FarmingPlanTask)
            .set({
              previous_task_id: '',
              task_chain_ids: ''
            })
            .where("name = :taskId", { taskId })
            .execute();

          // Fetch the updated task with all relations
          return await this.getTaskById(user, taskId);
        }
      }

      // Save the updated task using direct SQL update
      // We need to exclude relation fields that can't be updated via QueryBuilder
      const repo = AppDataSource.getRepository(FarmingPlanTask);

      // Create a clean copy without relation fields and computed properties
      const cleanUpdateData = { ...updateData };
      this.cleanUpdateDataObject(cleanUpdateData);

      // Only update if there are fields to update
      if (Object.keys(cleanUpdateData).length > 0) {
        await queryRunner.manager.createQueryBuilder()
          .update(FarmingPlanTask)
          .set(cleanUpdateData)
          .where("name = :taskId", { taskId })
          .execute();
      }

      // Handle warehouse items update if provided
      if (item_list !== undefined) {
        await this.updateWarehouseItems(queryRunner, user, taskId, item_list);
      }

      // Handle production quantities update if provided
      if (prod_quantity_list !== undefined) {
        await this.updateProductionQuantities(queryRunner, user, taskId, prod_quantity_list);
      }

      await queryRunner.commitTransaction();
      // Reload the task with all relations
      return await this.getTaskById(user, taskId);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      Logger.error(`Error updating task:`, error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Helper method to clean update data object by removing relation fields and computed properties
   * that can't be directly updated through TypeORM's QueryBuilder
   * @param updateData Object to clean
   */
  private cleanUpdateDataObject(updateData: any): void {
    if (!updateData) return;

    // Remove relation fields that can't be updated via QueryBuilder
    delete updateData.nextTasks;
    delete updateData.previousTask;
    delete updateData.farmingPlanState;
    delete updateData.assignedUser;
    delete updateData.department;
    delete updateData.environmentTemplate;
    delete updateData.template;
    delete updateData.vietplantsExtension;

    // Remove the newly added transfer relations
    delete updateData.incomingTransfers;
    delete updateData.outgoingTransfers;

    // Remove computed properties added by our API
    delete updateData.taskChainInfo;
    delete updateData.taskTransferInfo;
    delete updateData.productionInfo;
    delete updateData.warehouseItemsInfo;

    // Remove item lists that are handled separately
    delete updateData.item_list;
    delete updateData.prod_quantity_list;
  }

  /**
   * Create or update a farming plan task with upsert functionality
   * - If task name exists, update the task and related entities
   * - If task name doesn't exist, create new task and related entities
   * - For related arrays: upsert by name field, delete missing records
   */
  async createTask(user: ICurrentUser, createData: CreateFarmingPlanTaskDto): Promise<FarmingPlanTask> {
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const repo = queryRunner.manager.getRepository(FarmingPlanTask);

      // Generate unique task name if not provided
      if (!createData.name) {
        createData.name = uuidv4();
      }

      // Extract item_list and prod_quantity_list before creating task
      const { item_list, prod_quantity_list, ...taskDataWithoutLists } = createData;

      // Check if task already exists
      const existingTask = await repo.findOne({ where: { name: createData.name } });

      let taskName: string;

      if (existingTask) {
        // Update existing task
        Object.assign(existingTask, taskDataWithoutLists);
        existingTask.modified = new Date();
        existingTask.modified_by = user.user_id;

        await repo.save(existingTask);
        taskName = existingTask.name;

        Logger.info(`Updated existing task: ${taskName}`);
      } else {
        // Create new task
        // Set default values
        const taskData: any = {
          ...taskDataWithoutLists,
          created: new Date(),
          created_by: user.user_id,
          modified: new Date(),
          modified_by: user.user_id,
        };

        // Set ownership if available
        if (user.customer?.id) {
          taskData.owner = user.customer.id.toString();
        }

        const task = repo.create(taskData as any);
        const savedTask = await repo.save(task);

        // Handle both single task and array return types
        taskName = Array.isArray(savedTask) ? (savedTask[0] as any).name : (savedTask as any).name;

        Logger.info(`Created new task: ${taskName}`);
      }

      // Upsert warehouse items if provided
      if (item_list !== undefined) {
        await this.upsertWarehouseItems(queryRunner, user, taskName, item_list);
      }

      // Upsert production quantities if provided
      if (prod_quantity_list !== undefined) {
        await this.upsertProductionQuantities(queryRunner, user, taskName, prod_quantity_list);
      }

      await queryRunner.commitTransaction();

      // Auto-create label print requests for ENV_POUR tasks
      if ((createData.task_type as string) === 'ENV_POUR') {
        try {
          await this.labelPrintManagementService.autoCreateLabelRequests(
            taskName,
            user.user_id,
            user.customer_id
          );
          Logger.info(`Label print requests created for ENV_POUR task: ${taskName}`);
        } catch (labelError) {
          Logger.warn(`Failed to create label print requests for ENV_POUR task ${taskName}:`, labelError);
          // Don't fail the entire operation if label creation fails
          // This ensures backward compatibility and prevents task creation failures
        }
      }

      return await this.getTaskById(user, taskName);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      Logger.error(`Error creating task:`, error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Create or update an ENV_POUR task with task transfers using upsert functionality
   */
  async createEnvPourTask(user: ICurrentUser, createData: CreateFarmingPlanTaskDto & { task_transfers?: TaskTransferDto[] }): Promise<FarmingPlanTask> {
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Validate task type
      if ((createData.task_type as string) !== 'ENV_POUR') {
        throw new Error('This method is only for ENV_POUR tasks');
      }

      // Validate task transfers if provided
      if (createData.task_transfers && createData.task_transfers.length > 0) {
        await this.validateTaskTransfers(user, createData.task_transfers);
      }

      // Extract task transfers from createData
      const { task_transfers, ...taskDataWithoutTransfers } = createData;

      // Create/update the main task first (this will handle item_list and prod_quantity_list)
      const task = await this.createTask(user, taskDataWithoutTransfers as any);

      // Upsert task transfers if provided
      if (task_transfers !== undefined) {
        await this.upsertTaskTransfers(queryRunner, user, task.name, task_transfers);
      }

      await queryRunner.commitTransaction();

      // Auto-create label print requests for ENV_POUR tasks
      try {
        await this.labelPrintManagementService.autoCreateLabelRequests(
          task.name,
          user.user_id,
          user.customer_id
        );
        Logger.info(`Label print requests created for ENV_POUR task: ${task.name}`);
      } catch (labelError) {
        Logger.warn(`Failed to create label print requests for ENV_POUR task ${task.name}:`, labelError);
        // Don't fail the entire operation if label creation fails
        // This ensures backward compatibility and prevents task creation failures
      }

      return await this.getTaskById(user, task.name);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      Logger.error(`Error creating/updating ENV_POUR task:`, error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Bulk create tasks
   */
  async bulkCreateTasks(user: ICurrentUser, bulkData: BulkCreateTaskDto): Promise<FarmingPlanTask[]> {
    try {
      const results: FarmingPlanTask[] = [];

      for (const taskData of bulkData.tasks) {
        const task = await this.createTask(user, taskData as any);
        results.push(task);

        // Auto-create label print requests for ENV_POUR tasks
        if ((taskData.task_type as string) === 'ENV_POUR') {
          try {
            await this.labelPrintManagementService.autoCreateLabelRequests(
              task.name,
              user.user_id,
              user.customer_id
            );
            Logger.info(`Label print requests created for bulk ENV_POUR task: ${task.name}`);
          } catch (labelError) {
            Logger.warn(`Failed to create label print requests for bulk ENV_POUR task ${task.name}:`, labelError);
            // Don't fail the entire operation if label creation fails
          }
        }
      }

      return results;
    } catch (error) {
      Logger.error(`Error bulk creating tasks:`, error);
      throw error;
    }
  }

  /**
   * Delete a task by ID
   */
  async deleteTask(user: ICurrentUser, taskId: string): Promise<{ success: boolean; message: string }> {
    try {
      const repo = AppDataSource.getRepository(FarmingPlanTask);

      // Check if task exists
      const task = await repo.findOne({ where: { name: taskId } });
      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      // Delete related transfers first
      await this.deleteTaskTransfers(user, taskId);

      // Delete the task
      await repo.delete({ name: taskId });

      return {
        success: true,
        message: `Task ${taskId} deleted successfully`
      };
    } catch (error) {
      Logger.error(`Error deleting task:`, error);
      throw error;
    }
  }

  /**
   * Bulk delete tasks
   */
  async bulkDeleteTasks(user: ICurrentUser, bulkData: BulkDeleteTaskDto): Promise<{ success: boolean; deleted_count: number; message: string }> {
    try {
      const repo = AppDataSource.getRepository(FarmingPlanTask);
      let deletedCount = 0;

      for (const taskId of bulkData.task_ids) {
        try {
          await this.deleteTask(user, taskId);
          deletedCount++;
        } catch (error) {
          Logger.warn(`Failed to delete task ${taskId}:`, error);
        }
      }

      return {
        success: true,
        deleted_count: deletedCount,
        message: `Successfully deleted ${deletedCount} out of ${bulkData.task_ids.length} tasks`
      };
    } catch (error) {
      Logger.error(`Error bulk deleting tasks:`, error);
      throw error;
    }
  }

  /**
   * Get available source tasks for ENV_POUR task linking
   * Returns ENV_STOCK and ENV_STEAM_POT tasks that can be linked
   */
  async getAvailableSourceTasks(user: ICurrentUser, stateId?: string, departmentId?: string): Promise<FarmingPlanTask[]> {
    try {
      const repo = AppDataSource.getRepository(FarmingPlanTask);
      const queryBuilder = repo.createQueryBuilder('task');

      // Filter for ENV_STOCK and ENV_STEAM_POT tasks
      queryBuilder.where('task.task_type IN (:...taskTypes)', {
        taskTypes: ['ENV_STOCK', 'ENV_STEAM_POT']
      });

      // Add optional filters
      if (stateId) {
        queryBuilder.andWhere('task.farming_plan_state = :stateId', { stateId });
      }

      if (departmentId) {
        queryBuilder.andWhere('task.department_id = :departmentId', { departmentId });
      }

      // Order by creation date
      queryBuilder.orderBy('task.created', 'DESC');

      return await queryBuilder.getMany();
    } catch (error) {
      Logger.error(`Error getting available source tasks:`, error);
      throw error;
    }
  }

  /**
   * Validate task transfers for ENV_POUR tasks
   */
  private async validateTaskTransfers(user: ICurrentUser, transfers: TaskTransferDto[]): Promise<void> {
    const repo = AppDataSource.getRepository(FarmingPlanTask);

    for (const transfer of transfers) {
      // Check if source task exists
      const sourceTask = await repo.findOne({
        where: { name: transfer.source_task_id }
      });

      if (!sourceTask) {
        throw new Error(`Source task ${transfer.source_task_id} not found`);
      }

      // Validate source task type
      if (!['ENV_STOCK', 'ENV_STEAM_POT'].includes(transfer.source_task_type as string)) {
        throw new Error(`Invalid source task type: ${transfer.source_task_type}. Only ENV_STOCK and ENV_STEAM_POT are allowed`);
      }

      // Verify the source task type matches
      if ((sourceTask.task_type as string) !== (transfer.source_task_type as string)) {
        throw new Error(`Source task type mismatch: expected ${transfer.source_task_type}, got ${sourceTask.task_type}`);
      }
    }
  }

  /**
   * Create task transfers for ENV_POUR tasks
   * Note: This creates a simple task link without specific item transfers
   * For actual item transfers, use TaskItemTransferService
   */
  private async createTaskTransfers(user: ICurrentUser, targetTaskId: string, transfers: TaskTransferDto[]): Promise<void> {
    const transferRepo = AppDataSource.getRepository(TaskItemTransfer);

    for (const transfer of transfers) {
      // Create a placeholder transfer record to establish task linking
      // In a real scenario, you would specify actual items being transferred
      const transferEntity = new TaskItemTransfer();
      transferEntity.name = uuidv4();
      transferEntity.source_task_id = transfer.source_task_id;
      transferEntity.target_task_id = targetTaskId;
      transferEntity.item_id = 'PLACEHOLDER_ITEM'; // This should be replaced with actual item IDs
      transferEntity.quantity = 0; // Placeholder quantity
      transferEntity.conversion_factor = 1;
      transferEntity.description = transfer.description || `Task link from ${transfer.source_task_type} to ENV_POUR`;
      transferEntity.transfer_date = new Date();
      transferEntity.transfer_by = user.user_id;
      transferEntity.status = 'Linked';

      // Set ownership if available
      if (user.customer?.id) {
        transferEntity.owner = user.customer.id.toString();
      }

      await transferRepo.save(transferEntity);
    }
  }

  /**
   * Delete task transfers for a task
   */
  private async deleteTaskTransfers(user: ICurrentUser, taskId: string): Promise<void> {
    const transferRepo = AppDataSource.getRepository(TaskItemTransfer);

    // Delete both incoming and outgoing transfers
    await transferRepo.delete({ source_task_id: taskId });
    await transferRepo.delete({ target_task_id: taskId });
  }

  /**
   * Create warehouse items for a task
   */
  private async createWarehouseItems(queryRunner: any, user: ICurrentUser, taskId: string, itemList: WarehouseItemTaskUsedDto[]): Promise<void> {
    const warehouseItemRepo = queryRunner.manager.getRepository(WarehouseItemTaskUsed);

    for (const itemData of itemList) {
      const warehouseItem = new WarehouseItemTaskUsed();
      warehouseItem.name = itemData.name || uuidv4();
      warehouseItem.task_id = taskId;
      warehouseItem.iot_category_id = itemData.iot_category_id;
      warehouseItem.quantity = itemData.quantity;
      warehouseItem.description = itemData.description;
      warehouseItem.active_uom = itemData.active_uom;
      warehouseItem.active_conversion_factor = itemData.active_conversion_factor || 1;

      // Set default values for other fields
      warehouseItem.exp_quantity = 0;
      warehouseItem.loss_quantity = 0;
      warehouseItem.issued_quantity = 0;
      warehouseItem.total_qty_in_crop = 0;
      warehouseItem.draft_quantity = itemData.draft_quantity || 0;

      // Set ownership and audit fields
      if (user.customer?.id) {
        warehouseItem.owner = user.customer.id.toString();
      }
      warehouseItem.creation = new Date();
      warehouseItem.modified = new Date();
      warehouseItem.modified_by = user.user_id;

      await warehouseItemRepo.save(warehouseItem);
    }
  }

  /**
   * Create production quantities for a task
   */
  private async createProductionQuantities(queryRunner: any, user: ICurrentUser, taskId: string, prodQuantityList: ProductionQuantityDto[]): Promise<void> {
    const productionQuantityRepo = queryRunner.manager.getRepository(ProductionQuantity);

    for (const prodData of prodQuantityList) {
      const productionQuantity = new ProductionQuantity();
      productionQuantity.name = prodData.name || uuidv4();
      productionQuantity.task_id = taskId;
      productionQuantity.product_id = prodData.product_id;
      productionQuantity.quantity = prodData.quantity;
      productionQuantity.description = prodData.description;
      productionQuantity.active_uom = prodData.active_uom;
      productionQuantity.active_conversion_factor = prodData.active_conversion_factor || 1;

      // Set default values for other fields
      productionQuantity.exp_quantity = 0;
      productionQuantity.lost_quantity = 0;
      productionQuantity.finished_quantity = 0;
      productionQuantity.issued_quantity = 0;
      productionQuantity.total_qty_in_crop = 0;
      productionQuantity.draft_quantity = prodData.draft_quantity || 0;

      // Set ownership and audit fields
      if (user.customer?.id) {
        productionQuantity.owner = user.customer.id.toString();
      }
      productionQuantity.creation = new Date();
      productionQuantity.modified = new Date();
      productionQuantity.modified_by = user.user_id;

      await productionQuantityRepo.save(productionQuantity);
    }
  }

  /**
   * Update warehouse items for a task (replace all existing items)
   */
  private async updateWarehouseItems(queryRunner: any, user: ICurrentUser, taskId: string, itemList: WarehouseItemTaskUsedDto[]): Promise<void> {
    const warehouseItemRepo = queryRunner.manager.getRepository(WarehouseItemTaskUsed);

    // Delete existing warehouse items for this task
    await warehouseItemRepo.delete({ task_id: taskId });

    // Create new warehouse items if provided
    if (itemList && itemList.length > 0) {
      await this.createWarehouseItems(queryRunner, user, taskId, itemList);
    }
  }

  /**
   * Update production quantities for a task (replace all existing quantities)
   */
  private async updateProductionQuantities(queryRunner: any, user: ICurrentUser, taskId: string, prodQuantityList: ProductionQuantityDto[]): Promise<void> {
    const productionQuantityRepo = queryRunner.manager.getRepository(ProductionQuantity);

    // Delete existing production quantities for this task
    await productionQuantityRepo.delete({ task_id: taskId });

    // Create new production quantities if provided
    if (prodQuantityList && prodQuantityList.length > 0) {
      await this.createProductionQuantities(queryRunner, user, taskId, prodQuantityList);
    }
  }

  /**
   * Upsert warehouse items for a task
   * - If item has a name and exists, update it
   * - If item doesn't have a name or doesn't exist, create it
   * - Delete existing items not present in the incoming array
   */
  private async upsertWarehouseItems(queryRunner: any, user: ICurrentUser, taskId: string, itemList: WarehouseItemTaskUsedDto[]): Promise<void> {
    const warehouseItemRepo = queryRunner.manager.getRepository(WarehouseItemTaskUsed);

    // Get existing warehouse items for this task
    const existingItems = await warehouseItemRepo.find({ where: { task_id: taskId } });
    const existingItemsMap = new Map<string, WarehouseItemTaskUsed>(existingItems.map((item: WarehouseItemTaskUsed) => [item.name, item]));

    // Track which existing items are being updated
    const updatedItemNames = new Set<string>();

    // Process incoming items
    for (const itemData of itemList) {
      if (itemData.name && existingItemsMap.has(itemData.name)) {
        // Update existing item
        const existingItem = existingItemsMap.get(itemData.name)!;

        // Update fields
        existingItem.iot_category_id = itemData.iot_category_id;
        existingItem.quantity = itemData.quantity;
        existingItem.description = itemData.description;
        existingItem.active_uom = itemData.active_uom;
        existingItem.active_conversion_factor = itemData.active_conversion_factor || 1;
        existingItem.draft_quantity = itemData.draft_quantity || 0;

        // Update audit fields
        existingItem.modified = new Date();
        existingItem.modified_by = user.user_id;

        await warehouseItemRepo.save(existingItem);
        updatedItemNames.add(itemData.name);
      } else {
        // Create new item
        const warehouseItem = new WarehouseItemTaskUsed();
        warehouseItem.name = itemData.name || uuidv4();
        warehouseItem.task_id = taskId;
        warehouseItem.iot_category_id = itemData.iot_category_id;
        warehouseItem.quantity = itemData.quantity;
        warehouseItem.description = itemData.description;
        warehouseItem.active_uom = itemData.active_uom;
        warehouseItem.active_conversion_factor = itemData.active_conversion_factor || 1;

        // Set default values for other fields
        warehouseItem.exp_quantity = 0;
        warehouseItem.loss_quantity = 0;
        warehouseItem.issued_quantity = 0;
        warehouseItem.total_qty_in_crop = 0;
        warehouseItem.draft_quantity = itemData.draft_quantity || 0;

        // Set ownership and audit fields
        if (user.customer?.id) {
          warehouseItem.owner = user.customer.id.toString();
        }
        warehouseItem.creation = new Date();
        warehouseItem.modified = new Date();
        warehouseItem.modified_by = user.user_id;

        await warehouseItemRepo.save(warehouseItem);

        // Track the new item name if it was provided
        if (itemData.name) {
          updatedItemNames.add(itemData.name);
        }
      }
    }

    // Delete items that are not in the incoming array
    const itemsToDelete = existingItems.filter((item: WarehouseItemTaskUsed) => !updatedItemNames.has(item.name));
    if (itemsToDelete.length > 0) {
      await warehouseItemRepo.remove(itemsToDelete);
    }
  }

  /**
   * Upsert production quantities for a task
   * - If item has a name and exists, update it
   * - If item doesn't have a name or doesn't exist, create it
   * - Delete existing items not present in the incoming array
   */
  private async upsertProductionQuantities(queryRunner: any, user: ICurrentUser, taskId: string, prodQuantityList: ProductionQuantityDto[]): Promise<void> {
    const productionQuantityRepo = queryRunner.manager.getRepository(ProductionQuantity);

    // Get existing production quantities for this task
    const existingQuantities = await productionQuantityRepo.find({ where: { task_id: taskId } });
    const existingQuantitiesMap = new Map<string, ProductionQuantity>(existingQuantities.map((item: ProductionQuantity) => [item.name, item]));

    // Track which existing items are being updated
    const updatedQuantityNames = new Set<string>();

    // Process incoming production quantities
    for (const prodData of prodQuantityList) {
      if (prodData.name && existingQuantitiesMap.has(prodData.name)) {
        // Update existing production quantity
        const existingQuantity = existingQuantitiesMap.get(prodData.name)!;

        // Update fields
        existingQuantity.product_id = prodData.product_id;
        existingQuantity.quantity = prodData.quantity;
        existingQuantity.description = prodData.description;
        existingQuantity.active_uom = prodData.active_uom;
        existingQuantity.active_conversion_factor = prodData.active_conversion_factor || 1;
        existingQuantity.draft_quantity = prodData.draft_quantity || 0;

        // Update audit fields
        existingQuantity.modified = new Date();
        existingQuantity.modified_by = user.user_id;

        await productionQuantityRepo.save(existingQuantity);
        updatedQuantityNames.add(prodData.name);
      } else {
        // Create new production quantity
        const productionQuantity = new ProductionQuantity();
        productionQuantity.name = prodData.name || uuidv4();
        productionQuantity.task_id = taskId;
        productionQuantity.product_id = prodData.product_id;
        productionQuantity.quantity = prodData.quantity;
        productionQuantity.description = prodData.description;
        productionQuantity.active_uom = prodData.active_uom;
        productionQuantity.active_conversion_factor = prodData.active_conversion_factor || 1;

        // Set default values for other fields
        productionQuantity.exp_quantity = 0;
        productionQuantity.lost_quantity = 0;
        productionQuantity.finished_quantity = 0;
        productionQuantity.issued_quantity = 0;
        productionQuantity.total_qty_in_crop = 0;
        productionQuantity.draft_quantity = prodData.draft_quantity || 0;

        // Set ownership and audit fields
        if (user.customer?.id) {
          productionQuantity.owner = user.customer.id.toString();
        }
        productionQuantity.creation = new Date();
        productionQuantity.modified = new Date();
        productionQuantity.modified_by = user.user_id;

        await productionQuantityRepo.save(productionQuantity);

        // Track the new item name if it was provided
        if (prodData.name) {
          updatedQuantityNames.add(prodData.name);
        }
      }
    }

    // Delete quantities that are not in the incoming array
    const quantitiesToDelete = existingQuantities.filter((quantity: ProductionQuantity) => !updatedQuantityNames.has(quantity.name));
    if (quantitiesToDelete.length > 0) {
      await productionQuantityRepo.remove(quantitiesToDelete);
    }
  }

  /**
   * Upsert task transfers for a task
   * - Update existing transfers or create new ones
   * - Delete existing transfers not present in the incoming array
   */
  private async upsertTaskTransfers(queryRunner: any, user: ICurrentUser, targetTaskId: string, transfers: TaskTransferDto[]): Promise<void> {
    const transferRepo = queryRunner.manager.getRepository(TaskItemTransfer);

    // Get existing transfers for this target task
    const existingTransfers = await transferRepo.find({ where: { target_task_id: targetTaskId } });
    const existingTransfersMap = new Map<string, TaskItemTransfer>();

    // Create a map based on source_task_id for easier lookup
    existingTransfers.forEach((transfer: TaskItemTransfer) => {
      existingTransfersMap.set(transfer.source_task_id, transfer);
    });

    // Track which existing transfers are being updated
    const updatedSourceTaskIds = new Set<string>();

    // Process incoming transfers
    for (const transferData of transfers) {
      if (existingTransfersMap.has(transferData.source_task_id)) {
        // Update existing transfer
        const existingTransfer = existingTransfersMap.get(transferData.source_task_id)!;

        // Update fields
        existingTransfer.description = transferData.description || `Task link from ${transferData.source_task_type} to ENV_POUR`;
        existingTransfer.modified = new Date();
        existingTransfer.modified_by = user.user_id;

        await transferRepo.save(existingTransfer);
        updatedSourceTaskIds.add(transferData.source_task_id);
      } else {
        // Create new transfer
        const transferEntity = new TaskItemTransfer();
        transferEntity.name = uuidv4();
        transferEntity.source_task_id = transferData.source_task_id;
        transferEntity.target_task_id = targetTaskId;
        transferEntity.item_id = 'PLACEHOLDER_ITEM'; // This should be replaced with actual item IDs
        transferEntity.quantity = 0; // Placeholder quantity
        transferEntity.conversion_factor = 1;
        transferEntity.description = transferData.description || `Task link from ${transferData.source_task_type} to ENV_POUR`;
        transferEntity.transfer_date = new Date();
        transferEntity.transfer_by = user.user_id;

        // Set ownership and audit fields
        if (user.customer?.id) {
          transferEntity.owner = user.customer.id.toString();
        }
        transferEntity.creation = new Date();
        transferEntity.modified = new Date();
        transferEntity.modified_by = user.user_id;

        await transferRepo.save(transferEntity);
        updatedSourceTaskIds.add(transferData.source_task_id);
      }
    }

    // Delete transfers that are not in the incoming array
    const transfersToDelete = existingTransfers.filter((transfer: TaskItemTransfer) => !updatedSourceTaskIds.has(transfer.source_task_id));
    if (transfersToDelete.length > 0) {
      await transferRepo.remove(transfersToDelete);
    }
  }

  /**
   * Approve ENV_POUR task and change status to completed
   * Only for ENV_POUR task type
   */
  async approveEnvPourTask(user: ICurrentUser, approvalData: ApproveEnvPourTaskDto): Promise<{ success: boolean; message: string; task: FarmingPlanTask }> {
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the existing task
      const task = await this.getTaskById(user, approvalData.task_id);

      // Validate task type
      if ((task.task_type as string) !== 'ENV_POUR') {
        throw new HttpError(400, `Only ENV_POUR tasks can be approved through this API. Current task type: ${task.task_type}`);
      }

      // Check if task is in a state that can be approved
      if (task.status === 'Done') {
        throw new HttpError(400, `Task ${approvalData.task_id} is already completed`);
      }

      // Update task status to completed
      await queryRunner.manager.createQueryBuilder()
        .update(FarmingPlanTask)
        .set({
          status: 'Done',
          modified: new Date(),
          modified_by: user.user_id,
          // Add approval information if needed
          description: task.description ?
            `${task.description}\n\nApproved by: ${approvalData.approved_by || user.user_id}\nApproval notes: ${approvalData.approval_notes || 'No notes provided'}\nApproved at: ${new Date().toISOString()}` :
            `Approved by: ${approvalData.approved_by || user.user_id}\nApproval notes: ${approvalData.approval_notes || 'No notes provided'}\nApproved at: ${new Date().toISOString()}`
        })
        .where("name = :taskId", { taskId: approvalData.task_id })
        .execute();

      await queryRunner.commitTransaction();

      // Get updated task
      const updatedTask = await this.getTaskById(user, approvalData.task_id);

      return {
        success: true,
        message: `ENV_POUR task ${approvalData.task_id} has been approved and marked as completed`,
        task: updatedTask
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      Logger.error(`Error approving ENV_POUR task:`, error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get tasks with "In Progress" status for display
   */
  async getInProgressTasks(
    user: ICurrentUser,
    page: number = 1,
    size: number = 10,
    taskType?: string
  ): Promise<{ data: FarmingPlanTask[]; page: number; size: number; total: number }> {
    try {
      const skip = (page - 1) * size;
      const take = size;

      let qb = AppDataSource.getRepository(FarmingPlanTask)
        .createQueryBuilder('task')
        .leftJoin('task.farmingPlanState', 'state')
        .leftJoin('state.farmingPlan', 'farmingPlan')
        .leftJoin('farmingPlan.cropRelation', 'crop')
        .leftJoin('crop.zoneRelation', 'zone')
        .leftJoin('task.template', 'template')
        .leftJoin('task.assignedUser', 'assignedUser')
        .leftJoin('task.department', 'department')
        .leftJoin('task.environmentTemplate', 'environmentTemplate');

      // Select task fields
      qb = qb.select('task');

      // Select only required fields from related entities
      qb = qb.addSelect([
        'state.name', 'state.label',
        'farmingPlan.name',
        'crop.name', 'crop.label',
        'zone.name', 'zone.customer_id',
        'template.name', 'template.label',
        'assignedUser.name', 'assignedUser.email', 'assignedUser.first_name', 'assignedUser.last_name',
        'department.name', 'department.label',
        'environmentTemplate.name', 'environmentTemplate.label', 'environmentTemplate.environment_code'
      ]);

      // Filter for "In Progress" status
      qb = qb.where('task.deleted IS NULL')
        .andWhere('zone.customer_id = :customer_id', { customer_id: user.customer_id })
        .andWhere('task.status = :status', { status: 'In progress' });

      // Optional task type filter
      if (taskType) {
        qb = qb.andWhere('task.task_type = :taskType', { taskType });
      }

      // Apply pagination
      qb = qb.skip(skip).take(take);

      // Order by creation date
      qb = qb.orderBy('task.creation', 'DESC');

      // Execute query
      const [tasks, total] = await qb.getManyAndCount();

      return {
        data: tasks,
        page,
        size,
        total
      };
    } catch (error) {
      Logger.error('Error getting in progress tasks:', error);
      throw error;
    }
  }

  /**
   * Format tasks to match the old API response structure for backward compatibility
   * This ensures existing frontend code continues to work without changes
   */
  private async formatTasksForBackwardCompatibility(tasks: any[], user: ICurrentUser): Promise<any[]> {
    const formattedTasks = await Promise.all(tasks.map(async (task) => {
      // Get additional data that was included in the old API
      const [todoList, worksheetList, involveInUsers, assignedToInfo, statusDetail, tagInfo, cropTagInfo] = await Promise.all([
        this.getTodoListForTask(task.name),
        this.getWorksheetListForTask(task.name),
        this.getInvolveInUsersForTask(task.name),
        this.getAssignedToInfoForTask(task.assigned_to),
        this.getStatusDetailForTask(task.status),
        this.getTagInfoForTask(task.tag),
        this.getCropTagInfoForTask(task)
      ]);

      // Format item_list to match old API structure
      const itemList = task.warehouseItemsInfo?.items?.map((item: any) => ({
        name: item.name,
        quantity: item.quantity,
        exp_quantity: item.exp_quantity,
        loss_quantity: item.loss_quantity,
        issued_quantity: item.issued_quantity,
        draft_quantity: item.draft_quantity,
        description: item.description,
        task_id: item.task_id || task.name,
        iot_category_id: item.iot_category_id,
        label: item.category_label,
        item_name: item.category_name,
        active_uom: item.active_uom,
        active_uom_name: item.uom_name,
        active_conversion_factor: item.active_conversion_factor,
        uom_name: item.uom_name,
        uom_id: item.active_uom,
        conversion_factor: 1,
        uoms: [] // This would need to be populated if UOM conversion details are needed
      })) || [];

      // Format prod_quantity_list to match old API structure
      const prodQuantityList = task.productionInfo?.items?.map((prod: any) => ({
        name: prod.name,
        quantity: prod.quantity,
        exp_quantity: prod.exp_quantity,
        lost_quantity: prod.lost_quantity,
        issued_quantity: prod.issued_quantity,
        draft_quantity: prod.draft_quantity,
        finished_quantity: prod.finished_quantity,
        description: prod.description,
        task_id: prod.task_id || task.name,
        product_id: prod.product_id,
        label: prod.product_label,
        item_name: prod.product_name,
        active_uom: prod.active_uom,
        active_uom_name: prod.uom_name,
        active_conversion_factor: prod.active_conversion_factor,
        uom_name: prod.uom_name,
        uom_id: prod.active_uom,
        conversion_factor: 1,
        uoms: [] // This would need to be populated if UOM conversion details are needed
      })) || [];

      // Calculate todo counts
      const todoTotal = todoList?.length || 0;
      const todoDone = todoList?.filter((todo: any) => todo.is_completed === 1)?.length || 0;

      // Format the task to match old API structure exactly
      return {
        name: task.name,
        label: task.label,
        creation: task.creation,
        modified: task.modified,
        tag: task.tag,
        tag_label: tagInfo?.label,
        tag_color: tagInfo?.color,
        image: task.image,
        description: task.description,
        assigned_to: task.assigned_to,
        task_progress: parseFloat(task.task_progress?.toString() || '0'),
        department_id: task.department_id,
        task_type: task.task_type,
        department_label: task.department?.label,
        template_id: task.template_id,
        environment_template_id: task.environment_template_id,
        enviroment_template_label: task.environmentTemplate?.label,
        enviroment_template_environment_code: task.environmentTemplate?.environment_code,
        assigned_to_info: assignedToInfo,
        involve_in_users: involveInUsers,
        worksheet_list: worksheetList,
        item_list: itemList,
        todo_list: todoList,
        prod_quantity_list: prodQuantityList,
        start_date: task.start_date ? new Date(task.start_date).toISOString().replace('T', ' ').substring(0, 19) : null,
        end_date: task.end_date ? new Date(task.end_date).toISOString().replace('T', ' ').substring(0, 19) : null,
        status: task.status,
        status_detail: statusDetail,
        farming_plan_state: task.farmingPlanState?.name,
        state_name: task.farmingPlanState?.label,
        farming_plan: task.farmingPlanState?.farmingPlan?.name,
        plan_name: task.farmingPlanState?.farmingPlan?.label,
        crop_id: task.farmingPlanState?.farmingPlan?.cropRelation?.name,
        crop_name: task.farmingPlanState?.farmingPlan?.cropRelation?.label,
        crop_status: task.farmingPlanState?.farmingPlan?.cropRelation?.status,
        zone_id: task.farmingPlanState?.farmingPlan?.cropRelation?.zoneRelation?.name,
        zone_name: task.farmingPlanState?.farmingPlan?.cropRelation?.zoneRelation?.label,
        project_id: task.farmingPlanState?.farmingPlan?.cropRelation?.zoneRelation?.project_id,
        project_name: task.farmingPlanState?.farmingPlan?.cropRelation?.zoneRelation?.project?.label,
        customer_id: task.farmingPlanState?.farmingPlan?.cropRelation?.zoneRelation?.customer_id,
        todo_total: todoTotal,
        todo_done: todoDone,
        crop_tag_name: cropTagInfo?.name,
        crop_tag_label: cropTagInfo?.label,
        crop_tag_color: cropTagInfo?.color,

        // Include task transfer information for ENV_POUR tasks
        ...(task.task_type === 'ENV_POUR' && task.taskChainInfo?.itemsByParent ? {
          task_transfers: Object.entries(task.taskChainInfo.itemsByParent).map(([parentTaskId, items]: [string, any]) => ({
            source_task_id: parentTaskId,
            items: items
          }))
        } : {}),

        // Include debug information if available
        ...(task.debugInfo ? {
          debug_info: task.debugInfo
        } : {})
      };
    }));

    return formattedTasks;
  }

  /**
   * Helper methods for backward compatibility data fetching
   */
  private async getTodoListForTask(taskId: string): Promise<any[]> {
    try {
      // This would need to be implemented based on your todo entity structure
      // For now, return empty array to maintain compatibility
      return [];
    } catch (error) {
      Logger.error(`Error getting todo list for task ${taskId}:`, error);
      return [];
    }
  }

  private async getWorksheetListForTask(taskId: string): Promise<any[]> {
    try {
      // This would need to be implemented based on your worksheet entity structure
      // For now, return empty array to maintain compatibility
      return [];
    } catch (error) {
      Logger.error(`Error getting worksheet list for task ${taskId}:`, error);
      return [];
    }
  }

  private async getInvolveInUsersForTask(taskId: string): Promise<any[]> {
    try {
      // This would need to be implemented based on your involve users entity structure
      // For now, return empty array to maintain compatibility
      return [];
    } catch (error) {
      Logger.error(`Error getting involve in users for task ${taskId}:`, error);
      return [];
    }
  }

  private async getAssignedToInfoForTask(assignedTo?: string): Promise<any[]> {
    try {
      if (!assignedTo) return [];

      const user = await AppDataSource.getRepository(IotCustomerUser)
        .createQueryBuilder('user')
        .where('user.name = :assignedTo', { assignedTo })
        .getOne();

      if (!user) return [];

      return [{
        name: user.name,
        first_name: user.first_name,
        last_name: user.last_name,
        user_avatar: user.user_avatar
      }];
    } catch (error) {
      Logger.error(`Error getting assigned to info for ${assignedTo}:`, error);
      return [];
    }
  }

  private async getStatusDetailForTask(status?: string): Promise<any> {
    try {
      if (!status) return null;

      // This would need to be implemented based on your task status entity structure
      // For now, return basic structure to maintain compatibility
      return {
        label: status,
        color: '#000000',
        customer_id: null
      };
    } catch (error) {
      Logger.error(`Error getting status detail for ${status}:`, error);
      return null;
    }
  }

  private async getTagInfoForTask(tag?: string): Promise<any> {
    try {
      if (!tag) return null;

      // This would need to be implemented based on your tag entity structure
      // For now, return basic structure to maintain compatibility
      return {
        label: tag,
        color: '#000000'
      };
    } catch (error) {
      Logger.error(`Error getting tag info for ${tag}:`, error);
      return null;
    }
  }

  private async getCropTagInfoForTask(_task: any): Promise<any> {
    try {
      // This would need to be implemented based on your crop tag structure
      // For now, return null to maintain compatibility
      return null;
    } catch (error) {
      Logger.error(`Error getting crop tag info for task:`, error);
      return null;
    }
  }

  /**
   * Find matching production plan yield output for a task with item ID matching
   * @param user Current user for authorization
   * @param environmentTemplateId Environment template to match
   * @param startDate Task start date
   * @param endDate Task end date
   * @param productionPlanId Production plan ID to scope the search
   * @param itemId Item ID to match with yield output item_id
   */
  private async findMatchingYieldOutputWithItemId(
    user: ICurrentUser,
    environmentTemplateId: string,
    startDate: string,
    endDate: string,
    productionPlanId: string,
    itemId: string
  ): Promise<ProductionPlanYieldExpectedOutput | null> {
    try {
      const yieldOutput = await this.yieldExpectedOutputRepository
        .createQueryBuilder('yieldOutput')
        .innerJoin('yieldOutput.productionPlan', 'productionPlan')
        .where('yieldOutput.environment_template_id = :environmentTemplateId', { environmentTemplateId })
        .andWhere('yieldOutput.production_plan_id = :productionPlanId', { productionPlanId })
        .andWhere('productionPlan.customer_id = :customerId', { customerId: user.customer_id })
        .andWhere('yieldOutput.start_date <= :endDate', { endDate })
        .andWhere('yieldOutput.end_date >= :startDate', { startDate })
        .andWhere('yieldOutput.item_id = :itemId', { itemId })
        .getOne();

      return yieldOutput || null;
    } catch (error) {
      Logger.error(`Error finding matching yield output with item ID:`, error);
      return null;
    }
  }

  /**
   * Find matching production plan yield output for a task
   * @param user Current user for authorization
   * @param environmentTemplateId Environment template to match
   * @param startDate Task start date
   * @param endDate Task end date
   * @param productionPlanId Optional production plan ID to scope the search
   */
  private async findMatchingYieldOutput(
    user: ICurrentUser,
    environmentTemplateId: string,
    startDate: string,
    endDate: string,
    productionPlanId?: string
  ): Promise<ProductionPlanYieldExpectedOutput | null> {
    try {
      Logger.debug(`Finding yield output: environmentTemplateId=${environmentTemplateId}, productionPlanId=${productionPlanId || 'any'}, dateRange=${startDate} to ${endDate}`);

      let qb = this.yieldExpectedOutputRepository
        .createQueryBuilder('yieldOutput')
        .innerJoin('yieldOutput.productionPlan', 'productionPlan')
        .where('yieldOutput.environment_template_id = :environmentTemplateId', { environmentTemplateId })
        .andWhere('productionPlan.customer_id = :customerId', { customerId: user.customer_id })
        .andWhere('yieldOutput.start_date <= :endDate', { endDate })
        .andWhere('yieldOutput.end_date >= :startDate', { startDate });

      if (productionPlanId) {
        qb = qb.andWhere('yieldOutput.production_plan_id = :productionPlanId', { productionPlanId });
      }

      const yieldOutput = await qb.getOne();
      return yieldOutput || null;
    } catch (error) {
      Logger.error(`Error finding matching yield output:`, error);
      return null;
    }
  }

}