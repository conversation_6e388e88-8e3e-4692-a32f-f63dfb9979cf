# Enhanced API Response Format Implementation

## Overview

The upsert API response format has been enhanced to include related arrays in the same structure as the request body, providing better debugging capabilities and field mapping while maintaining backward compatibility.

## Key Enhancements

### 1. Direct Field Arrays in Response

The API response now includes the following arrays that match the request body structure:

- **`item_list`** - Warehouse items in request format
- **`prod_quantity_list`** - Production quantities in request format  
- **`task_transfers`** - Task transfers for ENV_POUR tasks in request format

### 2. Complete Field Mapping

Each item in the response arrays includes:
- **All request fields** - Fields sent in the original request
- **Additional database fields** - System-generated and audit fields
- **Related entity data** - Joined entity information

### 3. Backward Compatibility

Existing computed info objects are maintained:
- **`warehouseItemsInfo`** - Legacy warehouse items format
- **`productionInfo`** - Legacy production quantities format
- **`taskTransferInfo`** - Legacy task transfers format (ENV_POUR only)

## Response Structure

### Basic Task Response
```json
{
  "name": "task-001",
  "farming_plan_state": "state-123",
  "task_type": "ENV_STOCK",
  "label": "Task Label",
  "description": "Task Description",
  
  // Direct field arrays (NEW)
  "item_list": [...],
  "prod_quantity_list": [...],
  
  // Other task fields
  "start_date": "2024-06-20T00:00:00Z",
  "end_date": "2024-06-25T00:00:00Z",
  "status": "In Progress",
  "assigned_to": "user-id",
  "owner": "customer-id",
  "creation": "2024-06-20T10:00:00Z",
  "modified": "2024-06-20T10:00:00Z",
  "modified_by": "user-id",
  
  // Backward compatibility objects
  "warehouseItemsInfo": { "items": [...], "total_items": 2 },
  "productionInfo": { "items": [...], "total_items": 1 }
}
```

### ENV_POUR Task Response
```json
{
  "name": "pour-task-001",
  "task_type": "ENV_POUR",
  
  // Direct field arrays (NEW)
  "item_list": [...],
  "prod_quantity_list": [...],
  "task_transfers": [...],
  
  // Backward compatibility objects
  "warehouseItemsInfo": { "items": [...], "total_items": 1 },
  "productionInfo": { "items": [...], "total_items": 0 },
  "taskTransferInfo": { "transfers": [...], "total_transfers": 1 }
}
```

## Field Details

### item_list Array Structure
```json
{
  "name": "item-001",
  "quantity": 15,
  "description": "Item description",
  "iot_category_id": "category-123",
  "active_uom": "kg",
  "active_conversion_factor": 1,
  "draft_quantity": 5,
  
  // Additional database fields
  "exp_quantity": 0,
  "loss_quantity": 0,
  "issued_quantity": 0,
  "total_qty_in_crop": 0,
  "task_id": "task-001",
  "owner": "customer-id",
  "creation": "2024-06-20T10:00:00Z",
  "modified": "2024-06-20T10:00:00Z",
  "modified_by": "user-id",
  
  // Related entity data
  "iotCategory": { "name": "category-123", "label": "Category Label" },
  "activeUOM": { "name": "kg" }
}
```

### prod_quantity_list Array Structure
```json
{
  "name": "prod-001",
  "quantity": 20,
  "description": "Product description",
  "product_id": "product-123",
  "active_uom": "kg",
  "active_conversion_factor": 1,
  "draft_quantity": 10,
  
  // Additional database fields
  "exp_quantity": 0,
  "lost_quantity": 0,
  "finished_quantity": 0,
  "issued_quantity": 0,
  "total_qty_in_crop": 0,
  "task_id": "task-001",
  "owner": "customer-id",
  "creation": "2024-06-20T10:00:00Z",
  "modified": "2024-06-20T10:00:00Z",
  "modified_by": "user-id",
  
  // Related entity data
  "product": { "name": "product-123", "label": "Product Label" },
  "activeUOM": { "name": "kg" }
}
```

### task_transfers Array Structure (ENV_POUR only)
```json
{
  "source_task_id": "stock-task-001",
  "source_task_type": "ENV_STOCK",
  "description": "Transfer description",
  
  // Additional database fields
  "name": "transfer-uuid",
  "target_task_id": "pour-task-001",
  "item_id": "PLACEHOLDER_ITEM",
  "quantity": 0,
  "conversion_factor": 1,
  "transfer_date": "2024-06-20T10:00:00Z",
  "transfer_by": "user-id",
  "status": "Linked",
  "owner": "customer-id",
  "creation": "2024-06-20T10:00:00Z",
  "modified": "2024-06-20T10:00:00Z",
  "modified_by": "user-id",
  
  // Related entity data
  "sourceTask": { "name": "stock-task-001", "task_type": "ENV_STOCK" },
  "targetTask": { "name": "pour-task-001", "task_type": "ENV_POUR" }
}
```

## Benefits

### 1. Better Debugging
- Direct comparison between request and response
- Easy identification of field transformations
- Clear visibility of system-generated fields

### 2. Simplified Field Mapping
- No need for data transformation on frontend
- Direct use of response for subsequent requests
- Consistent field names and structure

### 3. Enhanced Development Experience
- Easier API testing and validation
- Improved error debugging
- Better documentation and examples

### 4. Backward Compatibility
- Existing integrations continue to work
- Gradual migration path available
- No breaking changes

## Implementation Details

### Service Layer Changes
- Enhanced `getTaskById()` method
- New helper methods for loading related data
- Proper TypeScript typing for response objects

### Response Building
- Direct field arrays built from entity data
- Backward compatibility objects maintained
- Efficient database queries with joins

### Performance Considerations
- Single query per entity type
- Proper use of database indexes
- Minimal overhead for enhanced format

## Migration Guide

### For New Integrations
Use the direct field arrays (`item_list`, `prod_quantity_list`, `task_transfers`) for all operations.

### For Existing Integrations
Continue using the computed info objects (`warehouseItemsInfo`, `productionInfo`, `taskTransferInfo`) while gradually migrating to the new format.

### Testing
Use the provided test scripts to verify the enhanced response format and ensure proper field mapping.
